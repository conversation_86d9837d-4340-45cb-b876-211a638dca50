import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        redirect: '/providers'
      },
      {
        path: '/providers',
        name: 'Providers',
        component: () => import('@/views/Providers.vue'),
        meta: { title: '服务账号管理' }
      },
      {
        path: '/model-groups',
        name: 'ModelGroups',
        component: () => import('@/views/ModelGroups.vue'),
        meta: { title: '模型组管理' }
      },
      {
        path: '/provider-models/:provider',
        name: 'ProviderModels',
        component: () => import('@/views/ProviderModels.vue'),
        meta: { title: '模型配置' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory('/aigateway-admin/'),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const authStore = useAuthStore()
  
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next('/login')
  } else if (to.path === '/login' && authStore.isAuthenticated) {
    next('/')
  } else {
    next()
  }
})

export default router
