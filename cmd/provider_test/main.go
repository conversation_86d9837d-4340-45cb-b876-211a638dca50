package main

import (
	"context"
	"goalfy_aigateway/internal/config"
	"goalfy_aigateway/internal/proto"
	"goalfy_aigateway/internal/services/adapters"
	"goalfy_aigateway/internal/storage"
	"log"

	"github.com/spf13/pflag"
)

var (
	testRequest = proto.ChatCompletionRequest{
		Model: "",
		Messages: []proto.ChatMessage{
			{
				Role:    "system",
				Content: "请输出思考过程，并使用tool回答最终结果",
			},
			{
				Role:    "user",
				Content: "如何将一只大象装进冰箱?",
			},
		},
		Temperature:      nil,
		TopP:             nil,
		N:                nil,
		Stream:           nil,
		Stop:             nil,
		MaxTokens:        nil,
		PresencePenalty:  nil,
		FrequencyPenalty: nil,
		LogitBias:        nil,
		User:             nil,
		Functions:        nil,
		FunctionCall:     nil,
		Tools: []proto.ChatCompletionTool{
			{
				Type: "function",
				Function: proto.ChatCompletionFunction{
					Name:        "answer",
					Description: "you must call this tool for final answer",
					Parameters: map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"content": map[string]interface{}{
								"type":        "string",
								"description": "(required) The final answer content",
							},
						},
						"required": []string{"content"},
					},
				},
			},
		},
		ToolChoice:     nil,
		Seed:           nil,
		ResponseFormat: nil,
		ServiceTier:    nil,
	}
)

func main() {
	var configPath string
	pflag.StringVar(&configPath, "config", "etc/config.yaml", "config file path")
	pflag.Parse()
	err := config.LoadConfig(configPath)
	if err != nil {
		log.Fatal(err)
	}

	// 初始化数据库连接
	db, err := storage.Open()
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	providerService := adapters.NewProviderService(db)
	modelStorage := storage.NewProviderStorage(db)
	clients := providerService.ListAdapter()

	for name, cli := range clients {
		if name != "claude" {
			continue
		}
		// 获取该提供商的模型列表
		models, err := modelStorage.GetModelsByProvider(name, false)
		if err != nil || len(models) == 0 {
			println("[❌❌]", name, "no models available")
			continue
		}

		copyReq := testRequest
		resp, _, err := cli.ChatCompletion(context.Background(), &copyReq, &models[0])
		if err != nil {
			println("[❌]", name, copyReq.Model, err.Error())
			continue
		}
		if len(resp.Choices[0].Message.ToolCalls) != 1 {
			println("[❌]", name, copyReq.Model, "没有按照期望调用工具")
			continue
		}
		println("[✅]", name, copyReq.Model)

	}

}
