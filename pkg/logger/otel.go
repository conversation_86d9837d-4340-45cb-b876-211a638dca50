package logger

import (
	"context"
	"go.opentelemetry.io/otel/log"
	"goalfy_aigateway/pkg/otel_init"
	"time"
)

func GetOtelLogger(instrumentName string) *OtelLogger {
	return &OtelLogger{
		logger: otel_init.GetLogger(instrumentName),
	}
}

type OtelLogger struct {
	logger log.Logger
}

func (l *OtelLogger) logEmit(ctx context.Context, event string, msg string, level log.Severity, attrs []log.KeyValue) {
	var v log.Record
	v.SetEventName(event)
	v.SetBody(log.StringValue(msg))
	v.SetTimestamp(time.Now())
	v.SetSeverity(level)
	v.AddAttributes(attrs...)
	v.AddAttributes(log.KeyValue{Key: "event", Value: log.StringValue(event)})
	l.logger.Emit(ctx, v)
}

func (l *OtelLogger) Debug(ctx context.Context, event string, msg string, attrs []log.KeyValue) {
	l.logEmit(ctx, event, msg, log.SeverityDebug, attrs)
}

func (l *OtelLogger) Info(ctx context.Context, event string, msg string, attrs ...log.KeyValue) {
	l.logEmit(ctx, event, msg, log.SeverityInfo, attrs)
}

func (l *OtelLogger) Error(ctx context.Context, event string, msg string, attrs ...log.KeyValue) {
	l.logEmit(ctx, event, msg, log.SeverityError, attrs)
}

func (l *OtelLogger) Warn(ctx context.Context, event string, msg string, attrs ...log.KeyValue) {
	l.logEmit(ctx, event, msg, log.SeverityWarn, attrs)
}
