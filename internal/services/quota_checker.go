package services

import (
	"context"
	"fmt"
	"go.uber.org/zap"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/proto"
	"goalfy_aigateway/internal/services/router"
	"goalfy_aigateway/internal/storage"
	"goalfy_aigateway/pkg/logger"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"sync"
	"time"
)

type QuotaChecker struct {
	db         storage.CommonDao
	modelRoute *router.WeightedModelRouter
	ruleMap    map[uint]*ruleQuotaCacheInfo
	l          sync.RWMutex
}

func NewQuotaChecker(db storage.CommonDao, modelRoute *router.WeightedModelRouter) *QuotaChecker {
	q := &QuotaChecker{
		db:         db,
		modelRoute: modelRoute,
		ruleMap:    make(map[uint]*ruleQuotaCacheInfo),
	}
	q.loadRules()
	return q
}

func (q *QuotaChecker) loadRules() {
	var lst []models.GWQuotaRule
	err := q.db.Find(&lst).Error
	if err != nil {
		logger.Fatal("DB List QuotaRule failed", zap.Error(err))
	}
	for _, r := range lst {
		q.ruleMap[r.ID] = &ruleQuotaCacheInfo{rule: r}
	}
}

func (q *QuotaChecker) Check(req proto.RequestTrace) error {
	var rules []*ruleQuotaCacheInfo
	q.l.RLock()
	for _, v := range q.ruleMap {
		if q.matchRule(v.rule, req) {
			rules = append(rules, v)
		}
	}
	q.l.RUnlock()
	for _, rc := range rules {
		if rc.IsArrived(q) {
			return fmt.Errorf("rule quota exceeded: %d", rc.rule.ID)
		}
	}
	return nil
}

func (q *QuotaChecker) getCheckRules(req proto.RequestTrace, rules []*ruleQuotaCacheInfo) []*ruleQuotaCacheInfo {
	var (
		projectRule  *ruleQuotaCacheInfo
		userRule     *ruleQuotaCacheInfo
		apiGroupRule *ruleQuotaCacheInfo
	)
	for _, v := range q.ruleMap {
		if v.rule.RuleType == 3 && v.rule.UserID != nil && *v.rule.UserID == req.UserId {
			userRule = v
		}
		if v.rule.RuleType == 2 && v.rule.ApiGroup != nil && *v.rule.ApiGroup == req.ApiGroup {
			apiGroupRule = v
		}
		if v.rule.RuleType == 1 && v.rule.ProjectID != nil && *v.rule.ProjectID == req.ProjectId {
			// 项目规则必须指定ApiGroup
			projectRule = v
		}

	}
	if projectRule != nil {
		// 如果by项目限制了，则只检查项目配额
		return []*ruleQuotaCacheInfo{projectRule}
	}
	if apiGroupRule != nil {
		// 如果by api组限制了，则只检查api组配额
		return []*ruleQuotaCacheInfo{apiGroupRule}
	}
	if userRule != nil {
		// 如果by用户限制了，则只检查用户配额
		return []*ruleQuotaCacheInfo{userRule}
	}

	return rules
}

func (q *QuotaChecker) matchRule(rule models.GWQuotaRule, req proto.RequestTrace) bool {
	if !rule.Enabled {
		return false
	}
	if rule.ApiGroup != nil && *rule.ApiGroup != "*" && *rule.ApiGroup != req.ApiGroup {
		return false
	}
	if rule.UserID != nil && *rule.UserID != "*" && req.UserId != *rule.UserID {
		return false
	}
	if rule.ProjectID != nil && *rule.ProjectID != "*" && req.ProjectId != *rule.ProjectID {
		return false
	}
	return true
}

func (q *QuotaChecker) Consume(req proto.RequestTrace, usage models.GWTokenUsage) {
	usage.ApiGroup = req.ApiGroup
	usage.UserID = req.UserId
	usage.ProjectID = req.ProjectId
	usage.ChatId = req.ChatId
	usage.Day = time.Now().Format("2006-01-02")
	var rules []*ruleQuotaCacheInfo
	q.l.RLock()
	for _, v := range q.ruleMap {
		if q.matchRule(v.rule, req) {
			rules = append(rules, v)
		}
	}
	q.l.RUnlock()
	for _, rule := range rules {
		rule.consume(q, &usage)
	}
	// 必须先计算配额，再保存到数据库；否则加载数据库会重复计算
	q.saveDB(&usage)
}

func (q *QuotaChecker) saveDB(usage *models.GWTokenUsage) {
	err := q.db.Clauses(clause.OnConflict{
		Columns: []clause.Column{
			{Name: "user_id"},
			{Name: "api_group"},
			{Name: "project_id"},
			{Name: "day"},
			{Name: "provider"},
			{Name: "model"},
			{Name: "chat_id"},
		},
		DoUpdates: clause.Assignments(map[string]interface{}{
			"prompt_token":       gorm.Expr("prompt_token + ?", usage.PromptToken),
			"completion_token":   gorm.Expr("completion_token + ?", usage.CompletionToken),
			"cache_create_token": gorm.Expr("cache_create_token + ?", usage.CacheCreateToken),
			"cache_read_token":   gorm.Expr("cache_read_token + ?", usage.CacheReadToken),
		}),
	}).Create(usage).Error
	if err != nil {
		logger.Error("Token save failed", zap.Error(err), zap.Any("usage", usage))
	}
}

func (q *QuotaChecker) GetArchivedDateQuota(rule models.GWQuotaRule, date string) (float64, bool) {
	db := q.db.Where("day=?  ", date)
	if rule.ApiGroup != nil && *rule.ApiGroup != "*" {
		db = db.Where("api_group = ?", *rule.ApiGroup)
	}
	if rule.UserID != nil && *rule.UserID != "*" {
		db = db.Where("user_id = ?", *rule.UserID)
	}
	if rule.ProjectID != nil && *rule.ProjectID != "*" {
		db = db.Where("project_id = ?", *rule.ProjectID)
	}
	var rowList []*models.GWQuotaDailyArchive
	err := db.Find(&rowList).Error
	if err != nil {
		logger.Error("DB List QuotaDailyArchive failed", zap.Error(err))
		return 0, false
	}
	if len(rowList) == 0 {
		return 0, false
	}
	var totalUsed float64
	for _, v := range rowList {
		totalUsed += v.QuotaUsed
	}
	return totalUsed, true
}

func (q *QuotaChecker) CalcDateQuota(rule models.GWQuotaRule, date string) (float64, error) {
	db := q.db.Where("day=?  ", date)
	if rule.ApiGroup != nil && *rule.ApiGroup != "*" {
		db = db.Where("api_group = ?", *rule.ApiGroup)
	}
	if rule.UserID != nil && *rule.UserID != "*" {
		db = db.Where("user_id = ?", *rule.UserID)
	}
	if rule.ProjectID != nil && *rule.ProjectID != "*" {
		db = db.Where("project_id = ?", *rule.ProjectID)
	}
	var rowList []*models.GWTokenUsage
	err := db.Find(&rowList).Error
	if err != nil {
		return 0, fmt.Errorf("DB List TokenUsage: %v", err)
	}
	var totalCost float64
	dimCosts := map[models.GWQuotaDailyArchive]float64{}
	for _, tr := range rowList {
		cost, ok := q.GetCostByModelUsage(tr)
		if !ok {
			logger.Error("Model Route Not Found", zap.String("provider", tr.Provider), zap.String("model", tr.Model))
			continue
		}
		totalCost += cost
		dk := models.GWQuotaDailyArchive{
			UserID:    tr.UserID,
			ApiGroup:  tr.ApiGroup,
			ProjectID: tr.ProjectID,
			Day:       date,
			Provider:  tr.Provider,
			Model:     tr.Model,
		}
		dimCosts[dk] += cost
	}
	if date != time.Now().Format("2006-01-02") {
		// 今天的token消耗不能归档
		if len(dimCosts) != 0 {
			for k, v := range dimCosts {
				k.QuotaUsed = v
				if err = q.db.Create(context.Background(), &k); err != nil {
					logger.Warn("QuotaDailyArchive save failed", zap.Error(err))
				}
			}
		} else {
			rec := &models.GWQuotaDailyArchive{
				Day: date,
			}
			if rule.ApiGroup != nil {
				rec.ApiGroup = *rule.ApiGroup
			}
			if rule.UserID != nil {
				rec.UserID = *rule.UserID
			}
			if rule.ProjectID != nil {
				rec.ProjectID = *rule.ProjectID
			}
			if err = q.db.Create(context.Background(), rec); err != nil {
				logger.Warn("QuotaDailyArchive save failed", zap.Error(err))
			}
		}
	}

	return totalCost, nil
}

func (q *QuotaChecker) GetCostByModelUsage(u *models.GWTokenUsage) (float64, bool) {
	cfg, ok := q.modelRoute.GetModel(u.Provider, u.Model)
	if !ok {
		return 0, false
	}
	cost := float64(u.PromptToken)/cfg.TokensInputPerQuota + float64(u.CompletionToken)/cfg.TokensOutputPerQuota
	if cfg.Provider == "claude" {
		cost += float64(u.CacheCreateToken)/cfg.TokensInputPerQuota*1.25 + float64(u.CacheReadToken)/cfg.TokensInputPerQuota*0.25
	}
	return cost, true
}
