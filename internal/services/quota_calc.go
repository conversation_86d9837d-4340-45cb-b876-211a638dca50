package services

import (
	"go.uber.org/zap"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/pkg/logger"
	"sync"
	"time"
)

type ruleQuotaCacheInfo struct {
	rule      models.GWQuotaRule
	Date      string
	totalUsed float64
	dailyUsed map[string]float64
	arrived   uint8 //0 未加载 1:未超限；2.超限；
	l         sync.RWMutex
}

func (r *ruleQuotaCacheInfo) IsArrived(q *QuotaChecker) bool {
	nowDate := time.Now().Format("2006-01-02")
	r.l.RLock()
	arrived := r.arrived
	date := r.Date
	r.l.RUnlock()

	if date == nowDate && arrived > 0 {
		return arrived == 2
	}
	r.l.Lock()
	defer r.l.Unlock()
	if r.arrived == 0 || date != nowDate {
		err := r.loadData(q, nowDate)
		if err != nil {
			logger.Error("GetDateQuota failed", zap.Error(err), zap.String("date", date), zap.Uint("rule_id", r.rule.ID))
			return true
		}

	}
	arrived = r.arrived

	return arrived == 2
}

func (r *ruleQuotaCacheInfo) loadData(q *QuotaChe<PERSON>, nowDate string) error {
	r.totalUsed = 0
	now, _ := time.ParseInLocation("2006-01-02", nowDate, nil)
	dateMap := map[string]float64{}
	dateMap[nowDate] = 0
	switch r.rule.Period {
	case "daily":
	//暂时不考虑weekly,涉及都周的计算方式
	//case "weekly":
	//	weekday := now.Weekday()
	//	if weekday == 0 { // Sunday
	//		weekday = 7
	//	}
	//	startOfWeek := now.AddDate(0, 0, -int(weekday-1))
	//	// 遍历本周过去的日期（不包括今天）
	//	for i := 0; i < int(weekday-1); i++ {
	//		date := startOfWeek.AddDate(0, 0, i)
	//		dateMap[date.Format("2006-01-02")] = 0
	//	}
	case "monthly":
		pre := now
		for {
			pre = pre.AddDate(0, 0, -1)
			if pre.Month() != now.Month() {
				break
			}
			dateMap[pre.Format("2006-01-02")] = 0
		}
	}
	//yesterday := now.AddDate(0, 0, -1).Format("2006-01-02")
	for date, _ := range dateMap {
		// 考虑到以下场景，3号进行了服务；1/2已完成archived; 4/5号没有任何请求；6号收到请求；尽管3号的cost是正确的；但是不会对3号进行archived
		//if cost, ok := r.dailyUsed[date]; ok && date < yesterday {
		//	// 这里需要排除昨天，昨天的数据需要GetDateQuota重新计算
		//	dateMap[date] = cost
		//	continue
		//}

		// 由于今天也会加载计算，因此在consume中需要先累加cost； 后累加token到数据库
		dateQuota, err := r.GetDateQuota(q, date)
		if err != nil {
			return err
		}
		dateMap[date] = dateQuota
		r.totalUsed += dateQuota
	}
	r.Date = nowDate
	r.dailyUsed = dateMap
	if r.totalUsed >= r.rule.QuotaLimit {
		r.arrived = 2
	} else {
		r.arrived = 1
	}
	return nil
}

func (r *ruleQuotaCacheInfo) GetDateQuota(q *QuotaChecker, date string) (float64, error) {
	used, ok := q.GetArchivedDateQuota(r.rule, date)
	if ok {
		return used, nil
	}
	used, err := q.CalcDateQuota(r.rule, date)
	if err != nil {
		return 0, err
	}
	return used, nil
}

func (r *ruleQuotaCacheInfo) consume(q *QuotaChecker, usage *models.GWTokenUsage) {
	cost, ok := q.GetCostByModelUsage(usage)
	if !ok {
		logger.Error("GetModelRoute failed", zap.String("provider", usage.Provider), zap.String("model", usage.Model))
		return
	}
	r.l.Lock()
	defer r.l.Unlock()
	if usage.Day != r.Date {
		// 由于规则具备周期性，当日期变更时，需要重新加载数据
		err := r.loadData(q, usage.Day)
		if err != nil {
			logger.Error("loadData failed", zap.String("date", usage.Day), zap.Uint("rule_id", r.rule.ID), zap.Error(err))
			return
		}
	}
	r.totalUsed += cost
	if r.totalUsed >= r.rule.QuotaLimit {
		r.arrived = 2
	}
}
