package adapters

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"strings"
	"time"

	"github.com/anthropics/anthropic-sdk-go"
	"github.com/anthropics/anthropic-sdk-go/option"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/proto"
)

type <PERSON> struct {
	client anthropic.Client
}

func NewClaudeClient(baseUrl, apiKey string) *<PERSON> {
	return &Claude{
		client: anthropic.NewClient(
			option.WithBaseURL(baseUrl),
			option.WithAPIKey(apiKey),
		),
	}
}

/*
*

	{
	  "id" : "msg_01K2WV79J7nLF2Awec8BiKfX",
	  "type" : "message",
	  "role" : "assistant",
	  "model" : "claude-sonnet-4-20250514",
	  "content" : [ {
	    "type" : "text",
	    "text" : "A quaternion is a mathematical system that extends complex numbers and is commonly used to represent rotations in 3D space. Here's an overview:\n\n## Mathematical Structure\n\nA quaternion is typically written as:\n**q = w + xi + yj + zk**\n\nWhere:\n- w, x, y, z are real numbers\n- i, j, k are the fundamental quaternion units\n\nThe fundamental units follow these multiplication rules:\n- i² = j² = k² = ijk = -1\n- ij = k, ji = -k\n- jk = i, kj = -i  \n- ki = j, ik = -j\n\n## Components\n\n- **w**: the scalar (real) part\n- **xi + yj + zk**: the vector (imaginary) part\n\n## Key Properties\n\n- **4D number system**: Quaternions form a 4-dimensional number system\n- **Non-commutative**: Unlike real/complex numbers, quaternion multiplication order matters (qp ≠ pq)\n- **Unit quaternions**: When |q| = 1, they efficiently represent 3D rotations\n\n## Main Applications\n\n1. **3D Computer Graphics**: Rotating objects, cameras, and coordinate systems\n2. **Robotics**: Controlling robot arm orientations and movements\n3. **Game Development**: Character and object rotations\n4. **Aerospace**: Spacecraft attitude control\n5. **Animation**: Smooth interpolation between rotations\n\n## Advantages for Rotations\n\n- **Compact**: Only 4 numbers vs 9 for rotation matrices\n- **No gimbal lock**: Unlike Euler angles\n- **Smooth interpolation**: Easy to blend between rotations\n- **Efficient composition**: Fast rotation combinations\n\nQuaternions were invented by Irish mathematician William Rowan Hamilton in 1843 and have become essential tools in modern 3D mathematics and computer graphics."
	  } ],
	  "stop_reason" : "end_turn",
	  "stop_sequence" : null,
	  "usage" : {
	    "input_tokens" : 13,
	    "cache_creation_input_tokens" : 0,
	    "cache_read_input_tokens" : 0,
	    "output_tokens" : 426,
	    "service_tier" : "standard"
	  }
	}
*/

func (c *Claude) ChatCompletion(ctx context.Context, req *proto.ChatCompletionRequest, model *models.GWModelConfig) (*proto.ChatCompletionResponse, error) {
	max_token := model.MaxTokens
	if req.MaxTokens != nil && *req.MaxTokens < max_token {
		max_token = *req.MaxTokens
	}
	params := anthropic.MessageNewParams{
		MaxTokens: int64(max_token),
		Model:     anthropic.Model(model.ModelName),
	}
	if req.Temperature != nil {
		params.Temperature = anthropic.Float(*req.Temperature)
	}

	// Convert messages and extract system prompt
	systemPrompts, messages, err := c.convertMessages(req.Messages)
	if err != nil {
		return nil, fmt.Errorf("failed to convert messages: %w", err)
	}
	params.System = systemPrompts
	params.Messages = messages

	// Handle tools
	if len(req.Tools) > 0 {
		tools := make([]anthropic.ToolUnionParam, len(req.Tools))
		for i, tool := range req.Tools {
			claudeTool := &anthropic.ToolParam{
				InputSchema: c.getToolParameter(tool.Function.Parameters),
				Name:        tool.Function.Name,
				Description: anthropic.String(tool.Function.Description),
			}
			if tool.Function.Cached {
				claudeTool.CacheControl = anthropic.NewCacheControlEphemeralParam()
			}
			tools[i] = anthropic.ToolUnionParam{OfTool: claudeTool}
		}
		params.Tools = tools

		// Handle tool choice
		if req.ToolChoice != nil {
			params.ToolChoice = c.convertToolChoice(*req.ToolChoice)
		}
	}
	res, err := c.client.Messages.New(ctx, params)
	if err != nil {
		/**
		{
		  "type" : "error",
		  "error" : {
		    "type" : "authentication_error",
		    "message" : "invalid x-api-key"
		  }
		}
		*/
		var apiErr *anthropic.Error
		if errors.As(err, &apiErr) {
			var protoErr proto.ErrorResponse
			jsoniter.UnmarshalFromString(apiErr.RawJSON(), &protoErr)
			return nil, &protoErr
		}
		return nil, fmt.Errorf("claude API call failed: %w", err)
	}

	// Process response content
	content, toolCalls, err := c.processResponseContent(res.Content)
	if err != nil {
		return nil, fmt.Errorf("failed to process response content: %w", err)
	}

	chat_message := proto.ChatMessage{
		Role:      string(res.Role),
		Content:   content,
		ToolCalls: toolCalls,
	}

	resp := &proto.ChatCompletionResponse{
		Raw:     []byte(res.RawJSON()),
		ID:      res.ID,
		Object:  "chat.completion",
		Created: time.Now().Unix(),
		Model:   string(res.Model),
		Choices: []proto.ChatCompletionChoice{
			{
				Index:        0,
				Message:      chat_message,
				FinishReason: string(res.StopReason),
			},
		},
		Usage: &proto.ChatCompletionUsage{
			PromptTokens:      res.Usage.InputTokens,
			CompletionTokens:  res.Usage.OutputTokens,
			TotalTokens:       res.Usage.InputTokens + res.Usage.OutputTokens + res.Usage.CacheReadInputTokens + res.Usage.CacheCreationInputTokens,
			CacheReadTokens:   res.Usage.CacheReadInputTokens,
			CacheCreateTokens: res.Usage.CacheCreationInputTokens,
		},
	}

	return resp, nil
}

// convertMessages converts OpenAI format messages to Claude format
func (c *Claude) convertMessages(messages []proto.ChatMessage) ([]anthropic.TextBlockParam, []anthropic.MessageParam, error) {
	var systemPrompts []anthropic.TextBlockParam
	var claudeMessages []anthropic.MessageParam

	for _, msg := range messages {
		switch msg.Role {
		case "system":
			// Claude handles system messages separately
			blocks, err := c.parseContentBlockParamUnion(msg)
			if err != nil {
				return nil, nil, err
			}
			for _, block := range blocks {
				if block.OfText != nil {
					systemPrompts = append(systemPrompts, *block.OfText)
				}
			}

		default:
			claudeMsg, err := c.convertMessage(msg)
			if err != nil {
				return nil, nil, fmt.Errorf("failed to convert message: %w", err)
			}
			claudeMessages = append(claudeMessages, claudeMsg)
		}
	}

	return systemPrompts, claudeMessages, nil
}

// convertMessage converts a single OpenAI message to Claude format
func (c *Claude) convertMessage(msg proto.ChatMessage) (anthropic.MessageParam, error) {
	switch msg.Role {
	case "user":
		return c.convertUserMessage(msg)
	case "assistant":
		return c.convertAssistantMessage(msg)
	case "tool":
		return c.convertToolMessage(msg)
	default:
		return anthropic.MessageParam{}, fmt.Errorf("unsupported message role: %s", msg.Role)
	}
}

// convertUserMessage converts OpenAI user message to Claude format
func (c *Claude) convertUserMessage(msg proto.ChatMessage) (anthropic.MessageParam, error) {
	contentBlocks, err := c.parseContentBlockParamUnion(msg)
	if err != nil {
		return anthropic.MessageParam{}, err
	}
	return anthropic.NewUserMessage(contentBlocks...), nil
}

// convertAssistantMessage converts OpenAI assistant message to Claude format
func (c *Claude) convertAssistantMessage(msg proto.ChatMessage) (anthropic.MessageParam, error) {
	var contentBlocks []anthropic.ContentBlockParamUnion
	contentBlocks, err := c.parseContentBlockParamUnion(msg)
	if err != nil {
		return anthropic.MessageParam{}, err
	}

	// Add tool use blocks if present
	for _, toolCall := range msg.ToolCalls {
		var input interface{}
		if toolCall.Function.Arguments != "" {
			if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &input); err != nil {
				return anthropic.MessageParam{}, fmt.Errorf("failed to parse tool arguments: %w", err)
			}
		}
		block := &anthropic.ToolUseBlockParam{
			ID:    toolCall.ID,
			Name:  toolCall.Function.Name,
			Input: input,
		}
		if msg.Cached {
			block.CacheControl = anthropic.NewCacheControlEphemeralParam()
		}
		toolUseBlock := anthropic.ContentBlockParamUnion{
			OfToolUse: block,
		}
		contentBlocks = append(contentBlocks, toolUseBlock)
	}

	if len(contentBlocks) == 0 {
		return anthropic.MessageParam{}, fmt.Errorf("assistant message has no content or tool calls")
	}

	return anthropic.NewAssistantMessage(contentBlocks...), nil
}

// convertToolMessage converts OpenAI tool message to Claude format
func (c *Claude) convertToolMessage(msg proto.ChatMessage) (anthropic.MessageParam, error) {
	// OpenAI tool messages have tool_call_id field
	if msg.ToolCallID == nil {
		return anthropic.MessageParam{}, fmt.Errorf("tool message missing tool_call_id")
	}
	// Get tool result content
	content := ""
	if contentStr, ok := msg.Content.(string); ok {
		if contentStr == "" {
			contentStr = "{}"
		}
		content = contentStr
	}
	toolBlock := &anthropic.ToolResultBlockParam{
		ToolUseID: *msg.ToolCallID,
		Content: []anthropic.ToolResultBlockParamContentUnion{
			{
				OfText: &anthropic.TextBlockParam{
					Text: content,
				},
			},
		},
	}

	if msg.Cached {
		toolBlock.CacheControl = anthropic.NewCacheControlEphemeralParam()
	}
	toolResultBlock := anthropic.ContentBlockParamUnion{
		OfToolResult: toolBlock,
	}

	return anthropic.NewUserMessage(toolResultBlock), nil
}

// getToolParameter converts OpenAI tool parameters to Claude format
func (c *Claude) getToolParameter(params map[string]interface{}) anthropic.ToolInputSchemaParam {
	// Convert the map to JSON and back to ensure proper structure
	jsonBytes, _ := json.Marshal(params)
	var schema anthropic.ToolInputSchemaParam
	json.Unmarshal(jsonBytes, &schema)
	return schema
}

// convertToolChoice converts OpenAI tool_choice to Claude format
func (c *Claude) convertToolChoice(toolChoice string) anthropic.ToolChoiceUnionParam {
	switch toolChoice {
	case "auto":
		return anthropic.ToolChoiceUnionParam{OfAuto: &anthropic.ToolChoiceAutoParam{}}
	case "none":
		return anthropic.ToolChoiceUnionParam{OfAny: &anthropic.ToolChoiceAnyParam{}}
	default:
		// For specific tool choice, we need to parse it
		// For now, default to auto
		return anthropic.ToolChoiceUnionParam{OfAuto: &anthropic.ToolChoiceAutoParam{}}
	}
}

// processResponseContent processes Claude response content blocks
func (c *Claude) processResponseContent(content []anthropic.ContentBlockUnion) (string, []proto.ToolCall, error) {
	var textParts []string
	var toolCalls []proto.ToolCall

	for _, block := range content {
		switch block := block.AsAny().(type) {
		case anthropic.TextBlock:
			textParts = append(textParts, block.Text)
		case anthropic.ToolUseBlock:
			// Convert tool use to OpenAI format
			inputJSON, err := json.Marshal(block.Input)
			if err != nil {
				return "", nil, fmt.Errorf("failed to marshal tool input: %w", err)
			}

			toolCall := proto.ToolCall{
				ID:   block.ID,
				Type: "function",
				Function: proto.FunctionCall{
					Name:      block.Name,
					Arguments: string(inputJSON),
				},
			}
			toolCalls = append(toolCalls, toolCall)
		}
	}

	content_text := strings.Join(textParts, "")
	return content_text, toolCalls, nil
}

func (c *Claude) parseContentBlockParamUnion(msg proto.ChatMessage) ([]anthropic.ContentBlockParamUnion, error) {
	if msg.Content == nil {
		return nil, nil
	}
	var contentBlocks []anthropic.ContentBlockParamUnion
	if contentStr, ok := msg.Content.(string); ok {
		if contentStr == "" {
			contentStr = "{}"
		}
		block := anthropic.TextBlockParam{
			Text: contentStr,
		}
		if msg.Cached {
			block.CacheControl = anthropic.NewCacheControlEphemeralParam()
		}
		contentBlocks = append(contentBlocks, anthropic.ContentBlockParamUnion{OfText: &block})
		return contentBlocks, nil
	}
	lst, ok := msg.Content.([]interface{})
	if !ok {
		return nil, fmt.Errorf("unsupported content type: %T", msg.Content)
	}
	for _, part := range lst {
		if partMap, ok := part.(map[string]interface{}); ok {
			if partType, exists := partMap["type"]; exists && partType == "text" {
				if text, exists := partMap["text"]; exists {
					if textStr, ok := text.(string); ok {
						block := anthropic.TextBlockParam{
							Text: textStr,
						}
						if msg.Cached || partMap["cached"] != nil && partMap["cached"].(bool) {
							block.CacheControl = anthropic.NewCacheControlEphemeralParam()
						}
						contentBlocks = append(contentBlocks, anthropic.ContentBlockParamUnion{OfText: &block})
					}
				}
			}
		}
	}
	return contentBlocks, nil
}
