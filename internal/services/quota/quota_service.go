package quota

import (
	"fmt"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/storage"
	"time"
	"gorm.io/gorm"
)

// QuotaService 配额服务
type QuotaService struct {
	db storage.CommonDao
}

// NewQuotaService 创建配额服务
func NewQuotaService(db storage.CommonDao) *QuotaService {
	return &QuotaService{db: db}
}

// QuotaRequest 配额请求
type QuotaRequest struct {
	UserID       string
	ApiGroup     string
	ProjectID    string
	Provider     string
	Model        string
	InputTokens  int
	OutputTokens int
}

// QuotaCheckResult 配额检查结果
type QuotaCheckResult struct {
	Allowed     bool                    `json:"allowed"`
	Cost        float64                 `json:"cost"`
	Message     string                  `json:"message,omitempty"`
	ViolatedRule *models.GWQuotaRule    `json:"violated_rule,omitempty"`
	Usages      []QuotaUsageInfo       `json:"usages,omitempty"`
}

// QuotaUsageInfo 配额使用信息
type QuotaUsageInfo struct {
	RuleID      uint    `json:"rule_id"`
	RuleName    string  `json:"rule_name"`
	RuleType    string  `json:"rule_type"`
	Limit       float64 `json:"limit"`
	Used        float64 `json:"used"`
	Remaining   float64 `json:"remaining"`
	Period      string  `json:"period"`
}

// CheckQuota 检查配额是否足够
func (s *QuotaService) CheckQuota(req *QuotaRequest) (*QuotaCheckResult, error) {
	// 计算配额成本
	cost, err := s.calculateQuotaCost(req)
	if err != nil {
		return nil, err
	}

	// 获取适用的配额规则
	rules, err := s.getApplicableRules(req.UserID, req.ApiGroup, req.ProjectID)
	if err != nil {
		return nil, err
	}

	// 检查每个规则
	var usages []QuotaUsageInfo
	for _, rule := range rules {
		usage, err := s.checkRuleQuota(&rule, req.UserID, req.ApiGroup, req.ProjectID, cost)
		if err != nil {
			return nil, err
		}

		usages = append(usages, *usage)

		// 如果任何规则被违反，返回失败
		if usage.Used + cost > usage.Limit {
			return &QuotaCheckResult{
				Allowed:      false,
				Cost:         cost,
				Message:      fmt.Sprintf("配额不足: %s (需要 %.2f，剩余 %.2f)", rule.Name, cost, usage.Remaining),
				ViolatedRule: &rule,
				Usages:       usages,
			}, nil
		}
	}

	return &QuotaCheckResult{
		Allowed: true,
		Cost:    cost,
		Usages:  usages,
	}, nil
}

// ConsumeQuota 消费配额
func (s *QuotaService) ConsumeQuota(req *QuotaRequest) error {
	// 计算配额成本
	cost, err := s.calculateQuotaCost(req)
	if err != nil {
		return err
	}

	// 获取适用的配额规则
	rules, err := s.getApplicableRules(req.UserID, req.ApiGroup, req.ProjectID)
	if err != nil {
		return err
	}

	// 在事务中更新所有适用规则的使用量
	return s.db.Transaction(func(tx *gorm.DB) error {
		for _, rule := range rules {
			err := s.updateRuleUsage(tx, &rule, req.UserID, req.ApiGroup, req.ProjectID, cost)
			if err != nil {
				return err
			}
		}
		return nil
	})
}

// GetUserQuotaStatus 获取用户配额状态
func (s *QuotaService) GetUserQuotaStatus(userID, apiGroup, projectID string) ([]QuotaUsageInfo, error) {
	rules, err := s.getApplicableRules(userID, apiGroup, projectID)
	if err != nil {
		return nil, err
	}

	var usages []QuotaUsageInfo
	for _, rule := range rules {
		usage, err := s.checkRuleQuota(&rule, userID, apiGroup, projectID, 0)
		if err != nil {
			return nil, err
		}
		usages = append(usages, *usage)
	}

	return usages, nil
}

// getApplicableRules 获取适用的配额规则
func (s *QuotaService) getApplicableRules(userID, apiGroup, projectID string) ([]models.GWQuotaRule, error) {
	var rules []models.GWQuotaRule

	query := s.db.Where("enabled = ?", true)

	// 构建查询条件，匹配所有可能适用的规则
	conditions := []string{
		"(rule_type = 'user_global' AND user_id = ? AND (api_group = '' OR api_group IS NULL) AND (project_id = '' OR project_id IS NULL))",
		"(rule_type = 'user_apigroup' AND user_id = ? AND api_group = ? AND (project_id = '' OR project_id IS NULL))",
		"(rule_type = 'user_project' AND user_id = ? AND (api_group = '' OR api_group IS NULL) AND project_id = ?)",
		"(rule_type = 'user_apigroup_project' AND user_id = ? AND api_group = ? AND project_id = ?)",
	}

	var args []interface{}
	var whereClause string

	for i, condition := range conditions {
		if i > 0 {
			whereClause += " OR "
		}
		whereClause += condition

		switch i {
		case 0: // user_global
			args = append(args, userID)
		case 1: // user_apigroup
			args = append(args, userID, apiGroup)
		case 2: // user_project
			args = append(args, userID, projectID)
		case 3: // user_apigroup_project
			args = append(args, userID, apiGroup, projectID)
		}
	}

	err := query.Where(whereClause, args...).Order("priority ASC").Find(&rules).Error
	if err != nil {
		return nil, err
	}

	return rules, nil
}

// checkRuleQuota 检查单个规则的配额使用情况
func (s *QuotaService) checkRuleQuota(rule *models.GWQuotaRule, userID, apiGroup, projectID string, additionalCost float64) (*QuotaUsageInfo, error) {
	period := s.getCurrentPeriod(rule.Period)

	var usage models.GWQuotaUsage
	err := s.db.Where("rule_id = ? AND user_id = ? AND period = ?", rule.ID, userID, period).
		First(&usage).Error

	if err == gorm.ErrRecordNotFound {
		// 如果没有使用记录，创建一个新的
		usage = models.GWQuotaUsage{
			RuleID:    rule.ID,
			UserID:    userID,
			ApiGroup:  apiGroup,
			ProjectID: projectID,
			Period:    period,
			Used:      0,
		}
	} else if err != nil {
		return nil, err
	}

	return &QuotaUsageInfo{
		RuleID:    rule.ID,
		RuleName:  rule.Name,
		RuleType:  rule.RuleType,
		Limit:     rule.QuotaLimit,
		Used:      usage.Used,
		Remaining: rule.QuotaLimit - usage.Used,
		Period:    period,
	}, nil
}

// updateRuleUsage 更新规则使用量
func (s *QuotaService) updateRuleUsage(tx *gorm.DB, rule *models.GWQuotaRule, userID, apiGroup, projectID string, cost float64) error {
	period := s.getCurrentPeriod(rule.Period)

	var usage models.GWQuotaUsage
	err := tx.Where("rule_id = ? AND user_id = ? AND period = ?", rule.ID, userID, period).
		First(&usage).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新的使用记录
		usage = models.GWQuotaUsage{
			RuleID:    rule.ID,
			UserID:    userID,
			ApiGroup:  apiGroup,
			ProjectID: projectID,
			Period:    period,
			Used:      cost,
		}
		return tx.Create(&usage).Error
	} else if err != nil {
		return err
	}

	// 更新使用量
	return tx.Model(&usage).Update("used", usage.Used+cost).Error
}

// getCurrentPeriod 获取当前周期标识
func (s *QuotaService) getCurrentPeriod(periodType string) string {
	now := time.Now()
	switch periodType {
	case "daily":
		return now.Format("2006-01-02")
	case "weekly":
		year, week := now.ISOWeek()
		return fmt.Sprintf("%d-W%02d", year, week)
	case "monthly":
		return now.Format("2006-01")
	case "yearly":
		return now.Format("2006")
	default:
		return now.Format("2006-01") // 默认月度
	}
}

// calculateQuotaCost 计算配额成本
func (s *QuotaService) calculateQuotaCost(req *QuotaRequest) (float64, error) {
	var modelConfig models.GWModelConfig
	err := s.db.Where("provider = ? AND model_name = ?", req.Provider, req.Model).
		First(&modelConfig).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果模型配置不存在，使用默认比率
			return float64(req.InputTokens)/1000 + float64(req.OutputTokens)/1000, nil
		}
		return 0, err
	}

	inputCost := float64(req.InputTokens) / modelConfig.TokensInputPerQuota
	outputCost := float64(req.OutputTokens) / modelConfig.TokensOutputPerQuota

	return inputCost + outputCost, nil
}
