package quota

import (
	"fmt"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/storage"
	"gorm.io/gorm"
)

// QuotaService 配额服务
type QuotaService struct {
	db storage.CommonDao
}

// NewQuotaService 创建配额服务
func NewQuotaService(db storage.CommonDao) *QuotaService {
	return &QuotaService{db: db}
}

// QuotaRequest 配额请求
type QuotaRequest struct {
	UserID       string
	ApiGroup     string
	Provider     string
	Model        string
	InputTokens  int
	OutputTokens int
}

// QuotaStatus 配额状态
type QuotaStatus struct {
	UserID    string  `json:"user_id"`
	ApiGroup  string  `json:"api_group"`
	Quota     float64 `json:"quota"`
	Used      float64 `json:"used"`
	Remaining float64 `json:"remaining"`
	TotalUsed float64 `json:"total_used"`
}

// CheckQuota 检查配额是否足够
func (s *QuotaService) CheckQuota(req *QuotaRequest) (bool, float64, error) {
	cost, err := s.calculateQuotaCost(req)
	if err != nil {
		return false, 0, err
	}

	userQuota, err := s.getUserQuota(req.UserID, req.ApiGroup)
	if err != nil {
		return false, cost, err
	}

	remaining := userQuota.Quota - userQuota.Used
	return remaining >= cost, cost, nil
}

// ConsumeQuota 消费配额
func (s *QuotaService) ConsumeQuota(req *QuotaRequest) error {
	cost, err := s.calculateQuotaCost(req)
	if err != nil {
		return err
	}

	return s.db.Transaction(func(tx *gorm.DB) error {
		var userQuota models.GWUserQuotaConfig
		err := tx.Where("user_id = ? AND api_group = ?", req.UserID, req.ApiGroup).
			First(&userQuota).Error
		if err != nil {
			if err == gorm.ErrRecordNotFound {
				// 创建默认配额
				userQuota = models.GWUserQuotaConfig{
					UserID:   req.UserID,
					ApiGroup: req.ApiGroup,
					Quota:    100000, // 默认配额
					Used:     0,
					TotalUsed: 0,
				}
				if err := tx.Create(&userQuota).Error; err != nil {
					return err
				}
			} else {
				return err
			}
		}

		// 检查配额是否足够
		if userQuota.Used+cost > userQuota.Quota {
			return fmt.Errorf("配额不足，需要 %.2f，剩余 %.2f", cost, userQuota.Quota-userQuota.Used)
		}

		// 更新使用量
		return tx.Model(&userQuota).Updates(map[string]interface{}{
			"used":       userQuota.Used + cost,
			"total_used": userQuota.TotalUsed + cost,
		}).Error
	})
}

// GetQuotaStatus 获取配额状态
func (s *QuotaService) GetQuotaStatus(userID, apiGroup string) (*QuotaStatus, error) {
	userQuota, err := s.getUserQuota(userID, apiGroup)
	if err != nil {
		return nil, err
	}

	return &QuotaStatus{
		UserID:    userQuota.UserID,
		ApiGroup:  userQuota.ApiGroup,
		Quota:     userQuota.Quota,
		Used:      userQuota.Used,
		Remaining: userQuota.Quota - userQuota.Used,
		TotalUsed: userQuota.TotalUsed,
	}, nil
}

// SetUserQuota 设置用户配额
func (s *QuotaService) SetUserQuota(userID, apiGroup string, quota float64) error {
	userQuota, err := s.getUserQuota(userID, apiGroup)
	if err != nil && err != gorm.ErrRecordNotFound {
		return err
	}

	if err == gorm.ErrRecordNotFound {
		// 创建新配额
		userQuota = &models.GWUserQuotaConfig{
			UserID:   userID,
			ApiGroup: apiGroup,
			Quota:    quota,
			Used:     0,
			TotalUsed: 0,
		}
		return s.db.Create(userQuota).Error
	}

	// 更新配额
	return s.db.Model(userQuota).Update("quota", quota).Error
}

// ResetUserQuota 重置用户配额使用量
func (s *QuotaService) ResetUserQuota(userID, apiGroup string) error {
	return s.db.Model(&models.GWUserQuotaConfig{}).
		Where("user_id = ? AND api_group = ?", userID, apiGroup).
		Update("used", 0).Error
}

// ListUserQuotas 列出所有用户配额
func (s *QuotaService) ListUserQuotas() ([]models.GWUserQuotaConfig, error) {
	var quotas []models.GWUserQuotaConfig
	err := s.db.Find(&quotas).Error
	return quotas, err
}

// getUserQuota 获取用户配额，如果不存在则创建默认配额
func (s *QuotaService) getUserQuota(userID, apiGroup string) (*models.GWUserQuotaConfig, error) {
	var userQuota models.GWUserQuotaConfig
	err := s.db.Where("user_id = ? AND api_group = ?", userID, apiGroup).
		First(&userQuota).Error
	
	if err == gorm.ErrRecordNotFound {
		// 创建默认配额
		userQuota = models.GWUserQuotaConfig{
			UserID:   userID,
			ApiGroup: apiGroup,
			Quota:    100000, // 默认配额
			Used:     0,
			TotalUsed: 0,
		}
		if err := s.db.Create(&userQuota).Error; err != nil {
			return nil, err
		}
		return &userQuota, nil
	}
	
	return &userQuota, err
}

// calculateQuotaCost 计算配额成本
func (s *QuotaService) calculateQuotaCost(req *QuotaRequest) (float64, error) {
	var modelConfig models.GWModelConfig
	err := s.db.Where("provider = ? AND model_name = ?", req.Provider, req.Model).
		First(&modelConfig).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			// 如果模型配置不存在，使用默认比率
			return float64(req.InputTokens)/1000 + float64(req.OutputTokens)/1000, nil
		}
		return 0, err
	}

	inputCost := float64(req.InputTokens) / modelConfig.TokensInputPerQuota
	outputCost := float64(req.OutputTokens) / modelConfig.TokensOutputPerQuota
	
	return inputCost + outputCost, nil
}
