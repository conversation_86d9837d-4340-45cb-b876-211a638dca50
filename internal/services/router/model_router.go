package router

import (
	"context"
	"fmt"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/storage"
	"goalfy_aigateway/pkg/logger"
	"math/rand"
	"strings"
	"sync"
	"time"

	"go.uber.org/zap"
)

// ModelRouter 模型路由器接口
type ModelRouter interface {
	// RouteModel 根据请求的模型名称路由到实际模型
	RouteModel(requestedModel string) (config *models.GWModelConfig, err error)
}

// WeightedModelRouter 加权模型路由器
type WeightedModelRouter struct {
	groupCache        map[string]*models.ModelGroupConfig
	modelsMap         map[string]map[string]*models.GWModelConfig
	cacheLock         sync.RWMutex
	rand              *rand.Rand
	modelGroupStorage *storage.ModelGroupStorage
	providerStorage   *storage.ProviderStorage
}

// NewWeightedModelRouter 创建新的加权模型路由器
func NewWeightedModelRouter(db storage.CommonDao) *WeightedModelRouter {
	source := rand.NewSource(time.Now().UnixNano())
	wm := &WeightedModelRouter{
		groupCache:        make(map[string]*models.ModelGroupConfig),
		modelsMap:         make(map[string]map[string]*models.GWModelConfig),
		rand:              rand.New(source),
		modelGroupStorage: storage.NewModelGroupStorage(db),
		providerStorage:   storage.NewProviderStorage(db),
	}
	wm.load()
	return wm
}
func (r *WeightedModelRouter) load() {
	providerMap, err := r.providerStorage.GetProviders(true, nil)
	if err != nil {
		logger.Fatal("failed to load providers from database", zap.Error(err))
	}
	for _, provider := range providerMap {
		modelList, err := r.providerStorage.GetModelsByProvider(provider.Name, true)
		if err != nil {
			logger.Fatal("failed to load models from database", zap.String("provider", provider.Name), zap.Error(err))
		}
		var nameList []string
		for _, model := range modelList {
			modelsMap, ok := r.modelsMap[provider.Name]
			if !ok {
				modelsMap = make(map[string]*models.GWModelConfig)
				r.modelsMap[provider.Name] = modelsMap
			}
			modelsMap[model.ModelName] = &model
			nameList = append(nameList, model.ModelName)
		}
		logger.Info(fmt.Sprintf("loaded models %s : %s", provider.Name, strings.Join(nameList, ", ")))
	}

	// 预加载所有活跃的模型组
	groups, err := r.modelGroupStorage.GetModelGroups(nil)
	if err != nil {
		logger.Fatal("failed to load model groups from database", zap.Error(err))
	}

	var groupNames []string
	for _, g := range groups {
		if len(g.Models) > 0 {
			r.groupCache[g.Name] = g
			groupNames = append(groupNames, g.Name)
		}
	}
	logger.Info("model groups loaded from database", zap.Strings("groups", groupNames))
	return
}

// RouteModel 根据请求的模型名称路由到实际模型
func (r *WeightedModelRouter) RouteModel(requestedModels string) (config *models.GWModelConfig, err error) {
	requestedModel := strings.SplitN(requestedModels, "/", 2)

	if len(requestedModel) < 2 {
		group, err := r.getModelGroup(requestedModel[0])
		if err != nil {
			return nil, err
		}
		if !group.Enabled {
			return nil, fmt.Errorf("model group '%s' is disabled", requestedModel[0])
		}
		switch group.Strategy {
		case "random":
			return r.randomStrategy(group)
		case "weighted":
			return r.weightedStrategy(group)
		}
		return r.weightedStrategy(group)
	}
	r.cacheLock.RLock()
	defer r.cacheLock.RUnlock()
	modelMap, ok := r.modelsMap[requestedModel[0]]
	if !ok {
		return nil, fmt.Errorf("provider '%s' not found", requestedModels)
	}
	model, ok := modelMap[requestedModel[1]]
	if !ok {
		return nil, fmt.Errorf("model '%s' not found", requestedModels)
	}
	if !model.Enabled {
		return nil, fmt.Errorf("model '%s' is disabled", requestedModels)
	}
	return model, nil
}

// randomStrategy 随机策略
func (r *WeightedModelRouter) randomStrategy(group *models.ModelGroupConfig) (model *models.GWModelConfig, err error) {
	var modelList []models.GWModelGroupItem
	for _, m := range group.Models {
		if m.Enabled {
			modelList = append(modelList, m)
		}
	}
	if len(modelList) == 0 {
		return nil, fmt.Errorf("no enabled models in group '%s'", group.Name)
	}
	idx := r.rand.Intn(len(modelList))
	return &modelList[idx].Model, nil
}

// weightedStrategy 加权策略
func (r *WeightedModelRouter) weightedStrategy(group *models.ModelGroupConfig) (model *models.GWModelConfig, err error) {
	// 计算总权重
	var totalWeight float64
	var modelList []models.GWModelGroupItem
	for _, m := range group.Models {
		if !m.Enabled {
			continue
		}
		modelList = append(modelList, m)
		totalWeight += m.Weight
	}
	if totalWeight <= 0 {
		// 如果总权重为0，使用随机策略
		return r.randomStrategy(group)
	}

	// 生成随机值
	value := r.rand.Float64() * totalWeight

	// 选择模型
	var accumulatedWeight float64
	for _, m := range modelList {
		accumulatedWeight += m.Weight
		if value <= accumulatedWeight {
			return &m.Model, nil
		}
	}
	// 如果没有选中任何模型（不应该发生），返回第一个
	return &modelList[0].Model, nil
}

// getModelGroup 获取模型组配置，使用缓存
func (r *WeightedModelRouter) getModelGroup(name string) (*models.ModelGroupConfig, error) {
	// 先检查缓存
	r.cacheLock.RLock()
	if group, ok := r.groupCache[name]; ok {
		r.cacheLock.RUnlock()
		return group, nil
	}
	r.cacheLock.RUnlock()
	return nil, fmt.Errorf("model '%s' not found", name)

}

func (r *WeightedModelRouter) AddModel(ctx context.Context, model *models.GWModelConfig) error {
	r.modelGroupStorage.Unscoped().Delete(model, "provider=? and model_name=?", model.Provider, model.ModelName)
	err := r.modelGroupStorage.Create(ctx, model)
	if err != nil {
		return err
	}
	r.cacheLock.Lock()
	defer r.cacheLock.Unlock()
	modelMap, ok := r.modelsMap[model.Provider]
	if !ok {
		modelMap = make(map[string]*models.GWModelConfig)
		r.modelsMap[model.Provider] = modelMap
	}
	modelMap[model.ModelName] = model
	return nil
}

func (r *WeightedModelRouter) UpdateModel(ctx context.Context, model models.GWModelConfig) error {
	err := r.modelGroupStorage.Save(ctx, &model)
	if err != nil {
		return err
	}
	r.cacheLock.Lock()
	defer r.cacheLock.Unlock()

	modelMap, ok := r.modelsMap[model.Provider]
	if !ok {
		modelMap = make(map[string]*models.GWModelConfig)
		r.modelsMap[model.Provider] = modelMap
	}
	modelMap[model.ModelName] = &model

	return nil
}

func (r *WeightedModelRouter) GetModel(provider, model string) (*models.GWModelConfig, bool) {
	r.cacheLock.Lock()
	defer r.cacheLock.Unlock()

	modelMap, ok := r.modelsMap[provider]
	if !ok {
		return nil, false
	}
	config, ok := modelMap[model]
	return config, ok
}

func (r *WeightedModelRouter) DeleteModel(ctx context.Context, model models.GWModelConfig) error {
	err := r.modelGroupStorage.Delete(ctx, &model)
	if err != nil {
		return err
	}

	r.cacheLock.Lock()
	defer r.cacheLock.Unlock()
	modelMap, ok := r.modelsMap[model.Provider]
	if ok {
		config, ok := modelMap[model.ModelName]
		if ok {
			config.Enabled = false
		}
	}
	return nil
}

func (r *WeightedModelRouter) UpdateGroup(ctx context.Context, group models.ModelGroupConfig) (*models.ModelGroupConfig, error) {
	err := r.modelGroupStorage.SaveGroup(ctx, &group)
	if err != nil {
		return nil, err
	}
	final, err := r.modelGroupStorage.GetModelGroupByName(group.Name)
	if err != nil {
		return nil, err
	}
	r.cacheLock.Lock()
	defer r.cacheLock.Unlock()
	r.groupCache[group.GWModelGroup.Name] = final
	return final, nil
}

func (r *WeightedModelRouter) CreateGroup(ctx context.Context, group models.ModelGroupConfig) (*models.ModelGroupConfig, error) {
	err := r.modelGroupStorage.CreateGroup(ctx, &group)
	if err != nil {
		return nil, err
	}
	final, err := r.modelGroupStorage.GetModelGroupByName(group.Name)
	if err != nil {
		return nil, err
	}
	r.cacheLock.Lock()
	defer r.cacheLock.Unlock()
	r.groupCache[group.GWModelGroup.Name] = final
	return final, nil
}
