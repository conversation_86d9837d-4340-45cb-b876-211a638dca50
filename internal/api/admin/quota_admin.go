package admin

import (
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/services"
	"goalfy_aigateway/internal/storage"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// QuotaAdminHandler 配额管理处理器
type QuotaAdminHandler struct {
	db           storage.CommonDao
	quotaService *services.QuotaChecker
}

// NewQuotaAdminHandler 创建配额管理处理器
func NewQuotaAdminHandler(db storage.CommonDao, quotaService *services.QuotaChecker) *QuotaAdminHandler {
	return &QuotaAdminHandler{
		db:           db,
		quotaService: quotaService,
	}
}

// CreateQuotaRuleRequest 创建配额规则请求
// 只保留GWQuotaRule实际存在的字段
type CreateQuotaRuleRequest struct {
	UserID     *string `json:"user_id"`
	ApiGroup   *string `json:"api_group"`
	ProjectID  *string `json:"project_id"`
	QuotaLimit float64 `json:"quota_limit" binding:"required,gt=0"`
	Period     string  `json:"period" binding:"required"`
}

// UpdateQuotaRuleRequest 更新配额规则请求
// 只保留GWQuotaRule实际存在的字段
type UpdateQuotaRuleRequest struct {
	QuotaLimit float64 `json:"quota_limit" binding:"gt=0"`
	Period     string  `json:"period"`
	Enabled    *bool   `json:"enabled"`
}

// QuotaStatusRequest 配额状态查询请求
type QuotaStatusRequest struct {
	UserID    string `form:"user_id" binding:"required"`
	ApiGroup  string `form:"api_group"`
	ProjectID string `form:"project_id"`
}

// CreateQuotaRule 创建配额规则
func (h *QuotaAdminHandler) CreateQuotaRule(c *gin.Context) {
	var req CreateQuotaRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "请求参数错误", "error": err.Error()})
		return
	}

	// 验证周期类型
	validPeriods := map[string]bool{
		"daily":   true,
		"weekly":  true,
		"monthly": true,
		"yearly":  true,
	}
	if !validPeriods[req.Period] {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的周期类型",
		})
		return
	}

	// 创建配额规则
	rule := models.GWQuotaRule{
		UserID:     req.UserID,
		ApiGroup:   req.ApiGroup,
		ProjectID:  req.ProjectID,
		Period:     req.Period,
		QuotaLimit: req.QuotaLimit,
		Enabled:    true,
	}
	if err := h.db.Create(c, &rule).Error; err != nil {
		c.JSON(500, gin.H{"error": err})
		return
	}

	// 同步缓存：添加新规则到缓存
	if h.quotaService != nil {
		h.quotaService.AddRule(rule)
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "配额规则创建成功", "data": rule})
}

// ListQuotaRules 列出配额规则
func (h *QuotaAdminHandler) ListQuotaRules(c *gin.Context) {
	var rules []models.GWQuotaRule

	query := h.db.Order("priority ASC, created_at DESC")

	// 支持按用户ID过滤
	if userID := c.Query("user_id"); userID != "" {
		query = query.Where("user_id = ?", userID)
	}

	// 支持按规则类型过滤
	if ruleType := c.Query("rule_type"); ruleType != "" {
		query = query.Where("rule_type = ?", ruleType)
	}

	// 支持按启用状态过滤
	if enabled := c.Query("enabled"); enabled != "" {
		if enabled == "true" {
			query = query.Where("enabled = ?", true)
		} else if enabled == "false" {
			query = query.Where("enabled = ?", false)
		}
	}

	if err := query.Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取配额规则失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rules,
	})
}

// GetQuotaRule 获取单个配额规则
func (h *QuotaAdminHandler) GetQuotaRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则ID",
		})
		return
	}

	var rule models.GWQuotaRule
	if err := h.db.First(&rule, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "配额规则不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "获取配额规则失败",
				"error":   err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rule,
	})
}

// UpdateQuotaRule 更新配额规则
func (h *QuotaAdminHandler) UpdateQuotaRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则ID",
		})
		return
	}

	var req UpdateQuotaRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	var rule models.GWQuotaRule
	if err := h.db.First(&rule, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "配额规则不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "获取配额规则失败",
				"error":   err.Error(),
			})
		}
		return
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.QuotaLimit > 0 {
		updates["quota_limit"] = req.QuotaLimit
	}
	if req.Period != "" {
		updates["period"] = req.Period
	}
	if req.Enabled != nil {
		updates["enabled"] = *req.Enabled
	}

	if err := h.db.Model(&rule).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新配额规则失败",
			"error":   err.Error(),
		})
		return
	}

	// 重新获取更新后的规则
	if err := h.db.First(&rule, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取更新后的规则失败",
			"error":   err.Error(),
		})
		return
	}

	// 同步缓存：更新缓存中的规则
	if h.quotaService != nil {
		h.quotaService.UpdateRule(rule)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配额规则更新成功",
		"data":    rule,
	})
}

// DeleteQuotaRule 删除配额规则
func (h *QuotaAdminHandler) DeleteQuotaRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"success": false, "message": "无效的规则ID"})
		return
	}

	if err := h.db.Delete(c, &models.GWQuotaRule{}, id).Error; err != nil {
		c.JSON(500, gin.H{"error": err})
		return
	}

	// 同步缓存：从缓存中移除规则
	if h.quotaService != nil {
		h.quotaService.RemoveRule(uint(id))
	}

	c.JSON(http.StatusOK, gin.H{"success": true, "message": "配额规则删除成功"})
}

// GetQuotaStatus 获取配额状态
func (h *QuotaAdminHandler) GetQuotaStatus(c *gin.Context) {
	var req QuotaStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 获取配额状态
	var usages []interface{}
	if h.quotaService != nil {
		if quotaUsages, err := h.quotaService.GetQuotaStatus(req.UserID, req.ApiGroup, req.ProjectID); err == nil {
			for _, usage := range quotaUsages {
				usages = append(usages, usage)
			}
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "获取配额状态失败",
				"error":   err.Error(),
			})
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"user_id":    req.UserID,
			"api_group":  req.ApiGroup,
			"project_id": req.ProjectID,
			"usages":     usages,
		},
	})
}

// ReloadQuotaCache 重新加载配额缓存
func (h *QuotaAdminHandler) ReloadQuotaCache(c *gin.Context) {
	if h.quotaService == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "配额服务未初始化",
		})
		return
	}

	if err := h.quotaService.ReloadRules(); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "重新加载配额缓存失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配额缓存重新加载成功",
	})
}
