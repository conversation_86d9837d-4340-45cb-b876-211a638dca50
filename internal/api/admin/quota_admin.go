package admin

import (
	"net/http"
	"strconv"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/services/quota"
	"goalfy_aigateway/internal/storage"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// QuotaAdminHandler 配额管理处理器
type QuotaAdminHandler struct {
	db           storage.CommonDao
	quotaService *quota.QuotaService
}

// NewQuotaAdminHandler 创建配额管理处理器
func NewQuotaAdminHandler(db storage.CommonDao) *QuotaAdminHandler {
	return &QuotaAdminHandler{
		db:           db,
		quotaService: quota.NewQuotaService(db),
	}
}

// CreateQuotaRuleRequest 创建配额规则请求
type CreateQuotaRuleRequest struct {
	Name        string  `json:"name" binding:"required"`
	RuleType    string  `json:"rule_type" binding:"required"`
	UserID      string  `json:"user_id"`
	ApiGroup    string  `json:"api_group"`
	ProjectID   string  `json:"project_id"`
	QuotaLimit  float64 `json:"quota_limit" binding:"required,gt=0"`
	Period      string  `json:"period" binding:"required"`
	Priority    int     `json:"priority"`
	Enabled     bool    `json:"enabled"`
	Description string  `json:"description"`
}

// UpdateQuotaRuleRequest 更新配额规则请求
type UpdateQuotaRuleRequest struct {
	Name        string  `json:"name"`
	QuotaLimit  float64 `json:"quota_limit" binding:"gt=0"`
	Period      string  `json:"period"`
	Priority    int     `json:"priority"`
	Enabled     *bool   `json:"enabled"`
	Description string  `json:"description"`
}

// QuotaStatusRequest 配额状态查询请求
type QuotaStatusRequest struct {
	UserID    string `form:"user_id" binding:"required"`
	ApiGroup  string `form:"api_group"`
	ProjectID string `form:"project_id"`
}

// CreateQuotaRule 创建配额规则
func (h *QuotaAdminHandler) CreateQuotaRule(c *gin.Context) {
	var req CreateQuotaRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	// 验证规则类型
	validRuleTypes := map[string]bool{
		"user_global":           true,
		"user_apigroup":         true,
		"user_project":          true,
		"user_apigroup_project": true,
	}
	if !validRuleTypes[req.RuleType] {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则类型",
		})
		return
	}

	// 验证周期类型
	validPeriods := map[string]bool{
		"daily":   true,
		"weekly":  true,
		"monthly": true,
		"yearly":  true,
	}
	if !validPeriods[req.Period] {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的周期类型",
		})
		return
	}

	// 创建配额规则
	rule := models.GWQuotaRule{
		Name:        req.Name,
		RuleType:    req.RuleType,
		UserID:      req.UserID,
		ApiGroup:    req.ApiGroup,
		ProjectID:   req.ProjectID,
		QuotaLimit:  req.QuotaLimit,
		Period:      req.Period,
		Priority:    req.Priority,
		Enabled:     req.Enabled,
		Description: req.Description,
	}

	if err := h.db.Create(&rule).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "创建配额规则失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配额规则创建成功",
		"data":    rule,
	})
}

// ListQuotaRules 列出配额规则
func (h *QuotaAdminHandler) ListQuotaRules(c *gin.Context) {
	var rules []models.GWQuotaRule
	
	query := h.db.Order("priority ASC, created_at DESC")
	
	// 支持按用户ID过滤
	if userID := c.Query("user_id"); userID != "" {
		query = query.Where("user_id = ?", userID)
	}
	
	// 支持按规则类型过滤
	if ruleType := c.Query("rule_type"); ruleType != "" {
		query = query.Where("rule_type = ?", ruleType)
	}
	
	// 支持按启用状态过滤
	if enabled := c.Query("enabled"); enabled != "" {
		if enabled == "true" {
			query = query.Where("enabled = ?", true)
		} else if enabled == "false" {
			query = query.Where("enabled = ?", false)
		}
	}

	if err := query.Find(&rules).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取配额规则失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rules,
	})
}

// GetQuotaRule 获取单个配额规则
func (h *QuotaAdminHandler) GetQuotaRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则ID",
		})
		return
	}

	var rule models.GWQuotaRule
	if err := h.db.First(&rule, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "配额规则不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "获取配额规则失败",
				"error":   err.Error(),
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    rule,
	})
}

// UpdateQuotaRule 更新配额规则
func (h *QuotaAdminHandler) UpdateQuotaRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则ID",
		})
		return
	}

	var req UpdateQuotaRuleRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	var rule models.GWQuotaRule
	if err := h.db.First(&rule, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"message": "配额规则不存在",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "获取配额规则失败",
				"error":   err.Error(),
			})
		}
		return
	}

	// 更新字段
	updates := make(map[string]interface{})
	if req.Name != "" {
		updates["name"] = req.Name
	}
	if req.QuotaLimit > 0 {
		updates["quota_limit"] = req.QuotaLimit
	}
	if req.Period != "" {
		updates["period"] = req.Period
	}
	if req.Priority != 0 {
		updates["priority"] = req.Priority
	}
	if req.Enabled != nil {
		updates["enabled"] = *req.Enabled
	}
	if req.Description != "" {
		updates["description"] = req.Description
	}

	if err := h.db.Model(&rule).Updates(updates).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "更新配额规则失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配额规则更新成功",
		"data":    rule,
	})
}

// DeleteQuotaRule 删除配额规则
func (h *QuotaAdminHandler) DeleteQuotaRule(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "无效的规则ID",
		})
		return
	}

	if err := h.db.Delete(&models.GWQuotaRule{}, id).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "删除配额规则失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "配额规则删除成功",
	})
}

// GetQuotaStatus 获取配额状态
func (h *QuotaAdminHandler) GetQuotaStatus(c *gin.Context) {
	var req QuotaStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"message": "请求参数错误",
			"error":   err.Error(),
		})
		return
	}

	usages, err := h.quotaService.GetUserQuotaStatus(req.UserID, req.ApiGroup, req.ProjectID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"message": "获取配额状态失败",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"user_id":    req.UserID,
			"api_group":  req.ApiGroup,
			"project_id": req.ProjectID,
			"usages":     usages,
		},
	})
}
