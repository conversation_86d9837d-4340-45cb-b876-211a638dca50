package admin

import (
	"goalfy_aigateway/internal/services"
	"goalfy_aigateway/internal/services/adapters"
	"goalfy_aigateway/internal/services/router"
	"goalfy_aigateway/internal/storage"

	"github.com/gin-gonic/gin"
)

// SetupRoutes 设置管理员路由
func SetupRoutes(admin *gin.RouterGroup, db storage.CommonDao, quotaService *services.QuotaChecker, modelRouter *router.WeightedModelRouter, clientFactory *adapters.ProviderService) {
	// 创建处理器
	providerHandler := NewProviderHandler(db, clientFactory)
	modelsHandler := NewModelsHandler(db, modelRouter)
	quotaHandler := NewQuotaAdminHandler(db, quotaService)
	//modelGroupHandler := NewModelGroupHandler(modelRouter)

	// 管理员路由组
	admin.Use(gin.Logger(), gin.Recovery())
	// 登录接口（不需要认证）
	admin.POST("/login", AdminLogin)

	// 需要认证的接口

	admin.Use(AdminAuthMiddleware())
	{
		// 供应商管理
		providers := admin.Group("/providers")
		{
			providers.GET("", providerHandler.GetProviders)            // 获取供应商列表
			providers.POST("", providerHandler.CreateProvider)         // 创建供应商
			providers.PUT("/:name", providerHandler.UpdateProvider)    // 更新供应商
			providers.DELETE("/:name", providerHandler.DeleteProvider) // 删除供应商

			// 供应商模型管理
			models := providers.Group("/models")
			{
				models.GET("/:provider", modelsHandler.GetProviderModels)                         // 获取供应商模型列表
				models.POST("/:provider", modelsHandler.CreateProviderModel)                      // 创建供应商模型
				models.PUT("/:provider/:model", modelsHandler.UpdateProviderModel)                // 更新供应商模型
				models.PATCH("/:provider/:model/status", modelsHandler.UpdateProviderModelStatus) // 更新供应商模型状态
				models.DELETE("/:provider/:model", modelsHandler.DeleteProviderModel)             // 删除供应商模型
			}
		}
		modelGroupHandler := NewModelGroupHandler(db, modelRouter)
		//模型组管理
		modelGroups := admin.Group("/model-groups")
		{
			modelGroups.GET("", modelGroupHandler.GetModelGroups)            // 获取模型组列表
			modelGroups.POST("", modelGroupHandler.CreateModelGroup)         // 创建模型组
			modelGroups.PUT("/:name", modelGroupHandler.UpdateModelGroup)    // 更新模型组
			modelGroups.DELETE("/:name", modelGroupHandler.DeleteModelGroup) // 删除模型组
		}

		// 配额管理
		quotas := admin.Group("/quotas")
		{
			quotas.GET("/rules", quotaHandler.ListQuotaRules)           // 获取配额规则列表
			quotas.POST("/rules", quotaHandler.CreateQuotaRule)         // 创建配额规则
			quotas.GET("/rules/:id", quotaHandler.GetQuotaRule)         // 获取单个配额规则
			quotas.PUT("/rules/:id", quotaHandler.UpdateQuotaRule)      // 更新配额规则
			quotas.DELETE("/rules/:id", quotaHandler.DeleteQuotaRule)   // 删除配额规则
			quotas.GET("/status", quotaHandler.GetQuotaStatus)          // 获取配额状态
			quotas.POST("/reload-cache", quotaHandler.ReloadQuotaCache) // 重新加载配额缓存
		}
	}

}
