package middlewares

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"goalfy_aigateway/internal/proto"
	"goalfy_aigateway/pkg/logger"
	"runtime/debug"
	"time"
)

var CtxHeaderKey = struct{}{}

func Trace() func(c *gin.Context) {
	tracer := otel.Tracer("http.server")
	meter := otel.Meter("http.server")
	httpRequestCount, err := meter.Int64Counter(
		"request_count",
		metric.WithDescription("Total number of HTTP requests"),
	)
	if err != nil {
		logger.Fatal("failed to create http request counter", zap.Error(err))
	}

	httpRequestDuration, err := meter.Int64Histogram(
		"request_duration",
		metric.WithDescription("Duration of HTTP requests in milliseconds"),
		metric.WithUnit("ms"),
	)
	if err != nil {
		logger.Fatal("failed to create http request duration histogram", zap.Error(err))
	}

	return func(c *gin.Context) {
		apiAuthInfo := c.MustGet(ctxAuthInfoKey).(ApiAuthInfo)
		var headInfo proto.RequestTrace
		if err := c.BindHeader(&headInfo); err != nil {
			logger.Warn("parse trace header failed", zap.Error(err))
		}
		headInfo.ApiGroup = apiAuthInfo.Grp
		ctx := context.WithValue(c.Request.Context(), CtxHeaderKey, headInfo)

		traceID, _ := trace.TraceIDFromHex(headInfo.TraceId)
		spanID, _ := trace.SpanIDFromHex(headInfo.SpanId)
		remoteCtx := trace.NewSpanContext(trace.SpanContextConfig{
			TraceID:    traceID,
			SpanID:     spanID,
			TraceFlags: trace.FlagsSampled, // 标记为已采样
		})
		ctx = trace.ContextWithRemoteSpanContext(ctx, remoteCtx)

		ctx, span := tracer.Start(ctx, "aigateway.call")

		startTime := time.Now()

		span.SetAttributes(attribute.String("api_group", apiAuthInfo.Grp))
		if headInfo.UserId != "" {
			span.SetAttributes(attribute.String("user_id", headInfo.UserId))
		}
		if headInfo.ProjectId != "" {
			span.SetAttributes(attribute.String("project_id", headInfo.ProjectId))
		}
		if headInfo.ChatId != "" {
			span.SetAttributes(attribute.String("chat_id", headInfo.ChatId))
		}
		if headInfo.RootChatId != "" {
			span.SetAttributes(attribute.String("root_chat_id", headInfo.RootChatId))
		}
		if headInfo.TaskId != "" {
			span.SetAttributes(attribute.String("task_id", headInfo.TaskId))
		}
		c.Request = c.Request.WithContext(ctx)
		c.Next()
		if c.Errors != nil {
			span.SetStatus(codes.Error, c.Errors.String())
			span.RecordError(err)
		}
		span.End()
		duration := time.Since(startTime).Milliseconds()
		httpRequestCount.Add(ctx, 1,
			metric.WithAttributes(
				attribute.String("api_group", apiAuthInfo.Grp),
				attribute.String("http.path", c.Request.URL.Path),
				attribute.Int("status_code", c.Writer.Status()),
			),
		)
		httpRequestDuration.Record(ctx, duration,
			metric.WithAttributes(
				attribute.String("http.path", c.Request.URL.Path),
				attribute.Int("status_code", c.Writer.Status()),
				attribute.String("api_group", apiAuthInfo.Grp),
			),
		)
	}
}

func Recovery() func(c *gin.Context) {
	return func(c *gin.Context) {
		defer func() {
			if v := recover(); v != nil {
				logger.Warn("recover", zap.Any("error", v))
				debug.PrintStack()
				err := fmt.Errorf("panic: %v", v)
				_ = c.Error(err)
				c.JSON(200, proto.Error(err))
			}
		}()
		c.Next()
	}
}
