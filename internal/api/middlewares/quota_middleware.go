package middlewares

import (
	"goalfy_aigateway/internal/services/quota"
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
)

// QuotaMiddleware 配额检查中间件
type QuotaMiddleware struct {
	quotaService *quota.QuotaService
}

// NewQuotaMiddleware 创建配额中间件
func NewQuotaMiddleware(quotaService *quota.QuotaService) *QuotaMiddleware {
	return &QuotaMiddleware{
		quotaService: quotaService,
	}
}

// QuotaCheckMiddleware 配额检查中间件
func (m *QuotaMiddleware) QuotaCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取用户信息
		userID := c.GetHeader("X-User-ID")
		apiGroup := c.GetHeader("X-Api-Group")
		projectID := c.GetHeader("X-Project-ID")

		// 如果没有提供API组信息，使用默认组
		if apiGroup == "" {
			apiGroup = "default"
		}

		// 如果没有用户ID，拒绝请求
		if userID == "" {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"success": false,
				"message": "缺少用户ID",
				"code":    "MISSING_USER_ID",
			})
			c.Abort()
			return
		}

		// 将用户信息存储到上下文中，供后续处理使用
		c.Set("user_id", userID)
		c.Set("api_group", apiGroup)
		c.Set("project_id", projectID)

		c.Next()
	}
}

// PreQuotaCheckMiddleware 预检查配额中间件（在解析请求参数后调用）
func (m *QuotaMiddleware) PreQuotaCheckMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "用户ID未找到",
				"code":    "INTERNAL_ERROR",
			})
			c.Abort()
			return
		}

		apiGroup, _ := c.Get("api_group")
		projectID, _ := c.Get("project_id")

		// 从上下文中获取模型信息（需要在路由处理中设置）
		provider, _ := c.Get("provider")
		model, _ := c.Get("model")
		inputTokens, _ := c.Get("input_tokens")
		outputTokens, _ := c.Get("output_tokens")

		// 如果模型信息不完整，跳过预检查
		if provider == nil || model == nil {
			c.Next()
			return
		}

		// 执行配额检查
		req := &quota.QuotaRequest{
			UserID:       userID.(string),
			ApiGroup:     getStringValue(apiGroup),
			ProjectID:    getStringValue(projectID),
			Provider:     provider.(string),
			Model:        model.(string),
			InputTokens:  getIntValue(inputTokens),
			OutputTokens: getIntValue(outputTokens),
		}

		result, err := m.quotaService.CheckQuota(req)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "配额检查失败",
				"code":    "QUOTA_CHECK_ERROR",
				"error":   err.Error(),
			})
			c.Abort()
			return
		}

		if !result.Allowed {
			c.JSON(http.StatusPaymentRequired, gin.H{
				"success":      false,
				"message":      result.Message,
				"code":         "QUOTA_EXCEEDED",
				"quota_cost":   result.Cost,
				"violated_rule": result.ViolatedRule,
				"usages":       result.Usages,
			})
			c.Abort()
			return
		}

		// 将检查结果存储到上下文中
		c.Set("quota_check_result", result)
		c.Next()
	}
}

// PostQuotaConsumeMiddleware 配额消费中间件（在请求成功后调用）
func (m *QuotaMiddleware) PostQuotaConsumeMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Next()

		// 只有在请求成功时才消费配额
		if c.Writer.Status() >= 200 && c.Writer.Status() < 300 {
			userID, _ := c.Get("user_id")
			apiGroup, _ := c.Get("api_group")
			projectID, _ := c.Get("project_id")
			provider, _ := c.Get("provider")
			model, _ := c.Get("model")
			inputTokens, _ := c.Get("actual_input_tokens")   // 实际使用的输入token
			outputTokens, _ := c.Get("actual_output_tokens") // 实际使用的输出token

			if userID != nil && provider != nil && model != nil {
				req := &quota.QuotaRequest{
					UserID:       userID.(string),
					ApiGroup:     getStringValue(apiGroup),
					ProjectID:    getStringValue(projectID),
					Provider:     provider.(string),
					Model:        model.(string),
					InputTokens:  getIntValue(inputTokens),
					OutputTokens: getIntValue(outputTokens),
				}

				// 异步消费配额，避免影响响应时间
				go func() {
					err := m.quotaService.ConsumeQuota(req)
					if err != nil {
						// 记录错误日志，但不影响响应
						// logger.Error("Failed to consume quota", zap.Error(err))
					}
				}()
			}
		}
	}
}

// QuotaStatusHandler 获取配额状态的处理器
func (m *QuotaMiddleware) QuotaStatusHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		userID := c.GetHeader("X-User-ID")
		apiGroup := c.GetHeader("X-Api-Group")
		projectID := c.GetHeader("X-Project-ID")

		if apiGroup == "" {
			apiGroup = "default"
		}

		if userID == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"success": false,
				"message": "缺少用户ID",
				"code":    "MISSING_USER_ID",
			})
			return
		}

		usages, err := m.quotaService.GetUserQuotaStatus(userID, apiGroup, projectID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{
				"success": false,
				"message": "获取配额状态失败",
				"code":    "QUOTA_STATUS_ERROR",
				"error":   err.Error(),
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"success": true,
			"data": gin.H{
				"user_id":    userID,
				"api_group":  apiGroup,
				"project_id": projectID,
				"usages":     usages,
			},
		})
	}
}

// getIntValue 安全地获取整数值
func getIntValue(value interface{}) int {
	if value == nil {
		return 0
	}

	switch v := value.(type) {
	case int:
		return v
	case int64:
		return int(v)
	case float64:
		return int(v)
	case string:
		if i, err := strconv.Atoi(v); err == nil {
			return i
		}
	}

	return 0
}

// getStringValue 安全地获取字符串值
func getStringValue(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	default:
		return ""
	}
}
