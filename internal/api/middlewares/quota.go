package middlewares

import (
	"github.com/gin-gonic/gin"
	"goalfy_aigateway/internal/proto"
	"goalfy_aigateway/internal/services"
	"net/http"
)

func QuotaCheck(checker *services.QuotaChecker) func(g *gin.Context) {
	return func(g *gin.Context) {
		rt := g.Value(CtxHeaderKey).(proto.RequestTrace)
		err := checker.Check(rt)
		if err != nil {
			g.JSON(http.StatusBadRequest, proto.Error(err))
			g.Abort()
			return
		}
		g.Next()
	}
}
