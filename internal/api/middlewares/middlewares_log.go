package middlewares

import (
	"bytes"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"goalfy_aigateway/pkg/logger"
	"io"
	"net/http"
	"strings"
	"time"
)

var (
	withRequestBody  = false
	withResponseBody = true

	excludeHeaders = map[string]bool{
		"Accept":             true,
		"Accept-Encoding":    true,
		"Content-Type":       true,
		"Content-Length":     true,
		"Accept-Language":    true,
		"Cache-Control":      true,
		"Pragma":             true,
		"Sec-Ch-Ua":          true,
		"Sec-Fetch-Sit":      true,
		"Sec-Ch-Ua-Mobile":   true,
		"Sec-Fetch-Mode":     true,
		"Sec-Ch-Ua-Platform": true,
	}
)

func header2Map(header http.Header, excludes map[string]bool) map[string]string {
	out := map[string]string{}
	for k, v := range header {
		if !excludes[k] {
			out[k] = strings.Join(v, " | ")
		}
	}
	return out
}

type responseWriter struct {
	gin.ResponseWriter
	buf bytes.Buffer
}

func (w *responseWriter) Write(data []byte) (n int, err error) {
	n, err = w.buf.Write(data)
	return
}

func (w *responseWriter) WriteString(s string) (n int, err error) {

	n, err = io.WriteString(&w.buf, s)

	return
}

func VerboseLogger() func(g *gin.Context) {

	return func(g *gin.Context) {
		path := g.Request.URL.Path

		fields := make([]zap.Field, 0, 5)
		start := time.Now()
		ww := &responseWriter{ResponseWriter: g.Writer}
		g.Writer = ww

		defer func() {
			data := ww.buf.Bytes()
			ww.ResponseWriter.Write(data)
			cost := time.Since(start)
			status := g.Writer.Status()
			if withResponseBody {
				if json.Valid(data) {
					fields = append(fields, zap.Any("response_body", json.RawMessage(data)))
				} else {
					fields = append(fields, zap.ByteString("response_body", data))
				}
			}

			if g.Errors != nil {
				fields = append(fields, zap.Any("errors", g.Errors.JSON()))
			}
			fields = append(fields, zap.Int("status", status), zap.Duration("cost", cost))
			logger.Info("http", fields...)
		}()

		if g.Request.URL.RawQuery != "" {
			path += "?" + g.Request.URL.RawQuery
		}
		clientIp := g.ClientIP()
		header := header2Map(g.Request.Header, excludeHeaders)
		fields = append(fields, zap.Any("request_headers", header))
		fields = append(fields, zap.String("path", path), zap.String("method", g.Request.Method), zap.String("client_ip", clientIp))

		if withRequestBody {
			if g.ContentType() != "multipart/form-data" {
				body, err := io.ReadAll(g.Request.Body)
				if err != nil {
					g.Abort()
					return
				}
				g.Request.Body = io.NopCloser(bytes.NewReader(body))
				if len(body) != 0 {
					if json.Valid(body) {
						fields = append(fields, zap.Any("request_body", json.RawMessage(body)))
					} else {
						fields = append(fields, zap.ByteString("request_body", body))
					}
				}
			}
		}
		g.Next()
	}
}
