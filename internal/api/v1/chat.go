package v1

import (
	"errors"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
	"goalfy_aigateway/internal/proto"
	"goalfy_aigateway/internal/services/adapters"
	"goalfy_aigateway/internal/services/router"
	"goalfy_aigateway/pkg/logger"
	"net/http"
)

type ChatHandler struct {
	modelRouter  router.ModelRouter
	agentFactory *adapters.ProviderService
}

func (h *ChatHandler) chatCompletion(c *gin.Context) {

	var req proto.ChatCompletionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Warn("parse request body failed", zap.Error(err))
		c.<PERSON>(http.StatusOK, proto.Error(err))
		return
	}
	if len(req.Messages) == 0 {
		c.JSON(http.StatusOK, proto.Error(errors.New("messages is empty")))
		return
	}
	model, err := h.modelRouter.RouteModel(req.Model)
	if err != nil {
		logger.Warn("route model failed", zap.String("model", req.Model), zap.Error(err))
		c.<PERSON>(http.StatusOK, proto.Error(err))
		return
	}
	client, err := h.agentFactory.GetClient(model.Provider)
	if err != nil {
		logger.Warn("get client failed", zap.String("provider", model.Provider), zap.Error(err))
		c.JSON(http.StatusOK, proto.Error(err))
		return
	}

	resp, err := llmMeasurer.Measure(c, client, &req, model)
	if err != nil {
		var errorResponse *proto.ErrorResponse
		if errors.As(err, &errorResponse) {
			c.JSON(http.StatusBadRequest, err)
		} else {
			c.JSON(http.StatusBadRequest, proto.Error(err))
		}
		return
	}
	c.JSON(http.StatusOK, resp)
}
