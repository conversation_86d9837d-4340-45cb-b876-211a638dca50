package v1

import (
	"github.com/gin-gonic/gin"
	"goalfy_aigateway/internal/api/middlewares"
	"goalfy_aigateway/internal/metrics"
	"goalfy_aigateway/internal/services"
	"goalfy_aigateway/internal/services/adapters"
	"goalfy_aigateway/internal/services/router"
)

var (
	llmMeasurer *metrics.LLMCallMeasurer
)

func SetupRoutes(g *gin.RouterGroup, quotaService *services.QuotaChecker, modelRouter router.ModelRouter, agentFactory *adapters.ProviderService) {
	llmMeasurer = metrics.NewLLMCallMeasurer(quotaService)
	v1 := ChatHandler{
		modelRouter:  modelRouter,
		agentFactory: agentFactory,
	}
	g.<PERSON>(middlewares.VerboseLogger(), middlewares.ApiAuth(), middlewares.Trace(), middlewares.Recovery(), middlewares.QuotaCheck(quotaService))
	g.POST("chat/completions", v1.chatCompletion)
}
