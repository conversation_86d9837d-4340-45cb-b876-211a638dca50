package models

import (
	"time"

	"gorm.io/gorm"
)

type GWProvider struct {
	Name       string `gorm:"type:varchar(64);primary_key"`
	ApiType    string `gorm:"type:varchar(64);notnull"`
	BaseUrl    string `gorm:"type:varchar(255);notnull"`
	ApiKey     string `gorm:"type:varchar(255);notnull"`
	Status     string `gorm:"type:varchar(16);default:'active'"` // active, inactive, maintenance
	Priority   int    `gorm:"type:int;default:100"`              // 优先级，数字越小优先级越高
	MaxRetries int    `gorm:"type:int;default:0"`                // 最大重试次数
	Timeout    int    `gorm:"type:int;default:30"`               // 超时时间(秒)
	CreatedAt  time.Time
	UpdatedAt  time.Time
	DeletedAt  gorm.DeletedAt `gorm:"index"`
}

type GWModelConfig struct {
	gorm.Model
	Provider             string  `gorm:"type:varchar(64);notnull;uniqueIndex:idx_provider_model"`
	ModelName            string  `gorm:"type:varchar(64);notnull;uniqueIndex:idx_provider_model"`
	TokensInputPerQuota  float64 `gorm:"type:double;notnull"`
	TokensOutputPerQuota float64 `gorm:"type:double;notnull"`
	MaxTokens            int     `gorm:"type:int;default:4096"`
	Temperature          float64 `gorm:"type:double;default:0.7"`
	TopP                 float64 `gorm:"type:double;default:1.0"`
	Enabled              bool    `gorm:"type:boolean;default:true"`

	// 外键约束
	ProviderRef GWProvider `gorm:"foreignKey:Provider;references:Name;constraint:OnUpdate:CASCADE,OnDelete:RESTRICT"`
}

// GWUserQuotaConfig 用户配额配置表
type GWUserQuotaConfig struct {
	UserID    string  `gorm:"type:varchar(64);primary_key"`
	ApiGroup  string  `gorm:"type:varchar(64);primary_key"`
	Quota     float64 `gorm:"type:double;not null;default:100000"`  // 配额总量
	Used      float64 `gorm:"type:double;not null;default:0"`       // 已使用配额
	TotalUsed float64 `gorm:"type:double;not null;default:0"`       // 历史总使用量
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`
}

// GWModelGroup 模型组配置表 - 支持A/B测试
type GWModelGroup struct {
	gorm.Model
	Name     string `gorm:"type:varchar(64);uniqueIndex;notnull"`
	Strategy string `gorm:"type:varchar(32);notnull"` // random, weighted
	Enabled  bool   `gorm:"type:boolean;default:true"`
}

// GWModelGroupItem 模型组成员表
type GWModelGroupItem struct {
	ID        uint `gorm:"primarykey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	GroupID uint `gorm:"index;notnull"`
	ModelID uint `gorm:"index;notnull"`

	Weight  float64 `gorm:"type:double;notnull;default:1.0"`
	Enabled bool    `gorm:"type:boolean;default:true"`

	// 外键约束 - 移除有问题的ModelConfig外键
	_group GWModelGroup  `gorm:"foreignKey:GroupID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Model  GWModelConfig `gorm:"foreignKey:ModelID;constraint:OnUpdate:CASCADE,OnDelete:RESTRICT"`
}
