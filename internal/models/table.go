package models

import (
	"time"

	"gorm.io/gorm"
)

type GWProvider struct {
	Name       string `gorm:"type:varchar(64);primary_key"`
	ApiType    string `gorm:"type:varchar(64);notnull"`
	BaseUrl    string `gorm:"type:varchar(255);notnull"`
	ApiKey     string `gorm:"type:varchar(255);notnull"`
	Status     string `gorm:"type:varchar(16);default:'active'"` // active, inactive, maintenance
	Priority   int    `gorm:"type:int;default:100"`              // 优先级，数字越小优先级越高
	MaxRetries int    `gorm:"type:int;default:0"`                // 最大重试次数
	Timeout    int    `gorm:"type:int;default:30"`               // 超时时间(秒)
	CreatedAt  time.Time
	UpdatedAt  time.Time
	DeletedAt  gorm.DeletedAt `gorm:"index"`
}

type GWModelConfig struct {
	gorm.Model
	Provider             string  `gorm:"type:varchar(64);notnull;uniqueIndex:idx_provider_model"`
	ModelName            string  `gorm:"type:varchar(64);notnull;uniqueIndex:idx_provider_model"`
	TokensInputPerQuota  float64 `gorm:"type:double;notnull"`
	TokensOutputPerQuota float64 `gorm:"type:double;notnull"`
	MaxTokens            int     `gorm:"type:int;default:4096"`
	Temperature          float64 `gorm:"type:double;default:0.7"`
	TopP                 float64 `gorm:"type:double;default:1.0"`
	Enabled              bool    `gorm:"type:boolean;default:true"`

	// 外键约束
	ProviderRef GWProvider `gorm:"foreignKey:Provider;references:Name;constraint:OnUpdate:CASCADE,OnDelete:RESTRICT"`
}

// GWQuotaRule 配额规则表
type GWQuotaRule struct {
	gorm.Model
	RuleType  int     `gorm:"not null;unique_index"`                  // 规则类型: project=1  api_group=2, user=3
	ApiGroup  *string `gorm:"type:varchar(64);unique_index"`          // API组
	UserID    *string `gorm:"type:varchar(64);unique_index"`          // 用户ID，为空表示全局规则
	ProjectID *string `gorm:"type:varchar(64);unique_index"`          // 项目ID，为空表示不限制
	Period    string  `gorm:"type:varchar(16);not null;unique_index"` // 周期: daily,  monthly

	QuotaLimit float64 `gorm:"type:double;not null"`      // 配额限制
	Enabled    bool    `gorm:"type:boolean;default:true"` // 是否启用
}

type GWTokenUsage struct {
	UserID           string `gorm:"type:varchar(64);primarykey"` // 用户ID
	ApiGroup         string `gorm:"type:varchar(64);primarykey"` // API组
	ProjectID        string `gorm:"type:varchar(64);primarykey"` // 项目ID
	Day              string `gorm:"type:varchar(32);primarykey"` //日期（quota统计最小单位)
	Provider         string `gorm:"type:varchar(64);primarykey"`
	Model            string `gorm:"type:varchar(64);primarykey"`
	ChatId           string `gorm:"type:varchar(64);primarykey"`
	PromptToken      int64  `gorm:"type:bigint(20);not null;default:0"`
	CompletionToken  int64  `gorm:"type:bigint(20);not null;default:0"`
	CacheCreateToken int64  `gorm:"type:bigint(20);not null;default:0"`
	CacheReadToken   int64  `gorm:"type:bigint(20);not null;default:0"`
}

// GWQuotaDailyArchive 每日配额消耗归档表
// 用于归档每天的配额消耗快照，便于历史查询和加速统计
// 归档维度与GWTokenUsage一致
type GWQuotaDailyArchive struct {
	ID        uint    `gorm:"primarykey"`
	UserID    string  `gorm:"type:varchar(64);index"` // 用户ID
	ApiGroup  string  `gorm:"type:varchar(64);index"` // API组
	ProjectID string  `gorm:"type:varchar(64);index"` // 项目ID
	Day       string  `gorm:"type:varchar(32);index"` // 日期（yyyy-MM-dd）
	Provider  string  `gorm:"type:varchar(64);index"`
	Model     string  `gorm:"type:varchar(64);index"`
	QuotaUsed float64 `gorm:"type:double;notnull"` // 当天消耗的配额单位
	CreatedAt time.Time
}

// GWModelGroup 模型组配置表 - 支持A/B测试
type GWModelGroup struct {
	gorm.Model
	Name     string `gorm:"type:varchar(64);uniqueIndex;notnull"`
	Strategy string `gorm:"type:varchar(32);notnull"` // random, weighted
	Enabled  bool   `gorm:"type:boolean;default:true"`
}

// GWModelGroupItem 模型组成员表
type GWModelGroupItem struct {
	ID        uint `gorm:"primarykey"`
	CreatedAt time.Time
	UpdatedAt time.Time
	DeletedAt gorm.DeletedAt `gorm:"index"`

	GroupID uint `gorm:"index;notnull"`
	ModelID uint `gorm:"index;notnull"`

	Weight  float64 `gorm:"type:double;notnull;default:1.0"`
	Enabled bool    `gorm:"type:boolean;default:true"`

	// 外键约束 - 移除有问题的ModelConfig外键
	_group GWModelGroup  `gorm:"foreignKey:GroupID;constraint:OnUpdate:CASCADE,OnDelete:CASCADE"`
	Model  GWModelConfig `gorm:"foreignKey:ModelID;constraint:OnUpdate:CASCADE,OnDelete:RESTRICT"`
}
