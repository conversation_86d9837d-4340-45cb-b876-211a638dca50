package metrics

import (
	"context"
	jsoniter "github.com/json-iterator/go"
	"go.opentelemetry.io/otel"
	"go.opentelemetry.io/otel/attribute"
	"go.opentelemetry.io/otel/codes"
	"go.opentelemetry.io/otel/log"
	"go.opentelemetry.io/otel/metric"
	"go.opentelemetry.io/otel/trace"
	"go.uber.org/zap"
	"goalfy_aigateway/internal/api/middlewares"
	"goalfy_aigateway/internal/models"
	"goalfy_aigateway/internal/proto"
	"goalfy_aigateway/internal/services/adapters"
	"goalfy_aigateway/pkg/logger"
	"time"
)

type LLMCallMeasurer struct {
	errCounter   metric.Int64Counter
	tokenCounter metric.Int64Counter
	olLog        *logger.OtelLogger
	tracer       trace.Tracer
}

func NewLLMCallMeasurer() *LLMCallMeasurer {
	otLog := logger.GetOtelLogger("llm_call")
	meter := otel.Meter("llm.call")
	ec, err := meter.Int64Counter("llm_call_errors",
		metric.WithDescription("Count of errors encountered during call."),
	)
	if err != nil {
		panic(err)
	}
	tc, err := meter.Int64Counter("llm_tokens",
		metric.WithDescription("Number of llm tokens"))
	m := &LLMCallMeasurer{
		errCounter:   ec,
		tokenCounter: tc,
		olLog:        otLog,
		tracer:       otel.Tracer("llm.client"),
	}
	return m
}

func (s *LLMCallMeasurer) Measure(ctx context.Context, cli adapters.AIClientAdapter, req *proto.ChatCompletionRequest, model *models.GWModelConfig) (*proto.ChatCompletionResponse, error) {
	rt := ctx.Value(middlewares.CtxHeaderKey).(proto.RequestTrace)

	ctx, span := s.tracer.Start(ctx, "llm.call")
	defer span.End()

	traceId := span.SpanContext().TraceID()
	spanId := span.SpanContext().SpanID()

	// 2. 设置span基础属性
	span.SetAttributes(
		attribute.String("provider", model.Provider),
		attribute.String("model", model.ModelName),
		attribute.String("api_group", rt.ApiGroup),
		attribute.String("user_id", rt.UserId),
	)

	if rt.ProjectId != "" {
		span.SetAttributes(attribute.String("project_id", rt.ProjectId))
	}
	if rt.RootChatId != "" {
		span.SetAttributes(attribute.String("root_chat_id", rt.RootChatId))
	}
	if rt.ChatId != "" {
		span.SetAttributes(attribute.String("chat_id", rt.ChatId))
	}
	if rt.TaskId != "" {
		span.SetAttributes(attribute.String("task_id", rt.TaskId))
	}
	logAttrs := []log.KeyValue{
		log.String("provider", model.Provider),
		log.String("model", model.ModelName),
		log.String("api_group", rt.ApiGroup),
		log.String("trace_id", traceId.String()),
		log.String("span_id", spanId.String()),
		log.String("user_id", rt.UserId),
		log.String("project_id", rt.ProjectId),
		log.String("root_chat_id", rt.RootChatId),
		log.String("chat_id", rt.ChatId),
	}
	if rt.TaskId != "" {
		logAttrs = append(logAttrs, log.String("task_id", rt.TaskId))
	}
	lastMessage := req.Messages[len(req.Messages)-1]
	content, err := jsoniter.MarshalToString(lastMessage)
	if err != nil {
		content = "failed to marshal last message:" + err.Error()
	}
	s.olLog.Info(ctx, "llm_request", content,
		logAttrs...,
	)

	start := time.Now()
	resp, err := cli.ChatCompletion(ctx, req, model)
	costMs := time.Since(start).Milliseconds()
	span.SetAttributes(attribute.Int64("duration_ms", costMs))
	if err != nil {
		logger.Error("Failed to call chat completion",
			zap.String("provider", model.Provider),
			zap.String("model", model.ModelName),
			zap.String("api_group", rt.ApiGroup),
			zap.String("user_id", rt.UserId),
			zap.String("trace_id", traceId.String()),
			zap.String("span_id", spanId.String()),
			zap.String("project_id", rt.ProjectId),
			zap.String("root_chat_id", rt.RootChatId),
			zap.String("chat_id", rt.ChatId),
			zap.Error(err))

		span.SetStatus(codes.Error, err.Error())
		span.RecordError(err)
		s.reportErr(ctx, req, model, costMs, err)
		return nil, err
	}
	span.SetAttributes(
		attribute.Int("prompt_tokens", int(resp.Usage.PromptTokens)),
		attribute.Int("completion_tokens", int(resp.Usage.CompletionTokens)),
	)
	if resp.Usage.CacheCreateTokens > 0 {
		span.SetAttributes(attribute.Int("cache_create_tokens", int(resp.Usage.CacheCreateTokens)))
	}
	if resp.Usage.CacheReadTokens > 0 {
		span.SetAttributes(attribute.Int("cache_read_tokens", int(resp.Usage.CacheReadTokens)))
	}
	span.End()

	// 统计模型耗时指标
	if resp.Usage.PromptTokens > 0 {
		s.tokenCounter.Add(ctx, int64(resp.Usage.PromptTokens), metric.WithAttributes(
			attribute.String("provider", model.Provider),
			attribute.String("model", model.ModelName),
			attribute.String("api_group", rt.ApiGroup),
			attribute.String("project_id", rt.ProjectId),
			attribute.String("user_id", rt.UserId),
			attribute.String("token_type", "prompt"),
		))
	}
	if resp.Usage.CompletionTokens > 0 {
		s.tokenCounter.Add(ctx, int64(resp.Usage.CompletionTokens), metric.WithAttributes(
			attribute.String("provider", model.Provider),
			attribute.String("model", model.ModelName),
			attribute.String("api_group", rt.ApiGroup),
			attribute.String("project_id", rt.ProjectId),
			attribute.String("user_id", rt.UserId),
			attribute.String("token_type", "completion"),
		))
	}
	if resp.Usage.CacheCreateTokens > 0 {
		s.tokenCounter.Add(ctx, int64(resp.Usage.CacheCreateTokens), metric.WithAttributes(
			attribute.String("provider", model.Provider),
			attribute.String("model", model.ModelName),
			attribute.String("api_group", rt.ApiGroup),
			attribute.String("project_id", rt.ProjectId),
			attribute.String("user_id", rt.UserId),
			attribute.String("token_type", "cache_create"),
		))
	}
	if resp.Usage.CacheReadTokens > 0 {
		s.tokenCounter.Add(ctx, int64(resp.Usage.CacheReadTokens), metric.WithAttributes(
			attribute.String("provider", model.Provider),
			attribute.String("model", model.ModelName),
			attribute.String("api_group", rt.ApiGroup),
			attribute.String("project_id", rt.ProjectId),
			attribute.String("user_id", rt.UserId),
			attribute.String("token_type", "cache_read"),
		))
	}
	s.olLog.Info(ctx, "llm_response", string(resp.Raw),
		logAttrs...,
	)
	return resp, nil
}

func (s *LLMCallMeasurer) reportErr(ctx context.Context, req *proto.ChatCompletionRequest, model *models.GWModelConfig, costMs int64, err error) {
	s.errCounter.Add(ctx, 1, metric.WithAttributes(
		attribute.String("provider", model.Provider),
		attribute.String("model", model.ModelName)))
}
