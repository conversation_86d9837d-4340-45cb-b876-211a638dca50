package storage

import (
	"fmt"
	"goalfy_aigateway/internal/models"
)

// ProviderStorage 提供商服务
type ProviderStorage struct {
	CommonDao
}

// NewProviderStorage 创建新的提供商服务
func NewProviderStorage(db CommonDao) *ProviderStorage {
	return &ProviderStorage{CommonDao: db}
}

// GetProviderByName 根据名称获取提供商
func (s *ProviderStorage) GetProviderByName(name string) (*models.GWProvider, error) {
	var provider models.GWProvider
	err := s.DB.Where("name = ? AND status = ?", name, "active").First(&provider).Error
	if err != nil {
		return nil, fmt.Errorf("未找到提供商: %s", name)
	}
	return &provider, nil
}

// GetProviders 获取所有活跃的提供商
func (s *ProviderStorage) GetProviders(withDeleted bool, conditions map[string]interface{}) ([]models.GWProvider, error) {
	var providers []models.GWProvider
	if withDeleted {
		err := s.DB.Unscoped().Where(conditions).Find(&providers).Error
		return providers, err
	}
	err := s.DB.Where(conditions).Find(&providers).Error
	return providers, err
}

// GetModelsByProvider 根据提供商获取模型列表
func (s *ProviderStorage) GetModelsByProvider(providerName string, withDeleted bool) ([]models.GWModelConfig, error) {
	var modelList []models.GWModelConfig
	if withDeleted {
		err := s.DB.Unscoped().Where("provider = ? ", providerName).Find(&modelList).Error
		return modelList, err
	}
	err := s.DB.Where("provider = ?", providerName, true).Find(&modelList).Error
	return modelList, err
}

// GetModelConfig 获取特定模型配置
func (s *ProviderStorage) GetModelConfig(provider, modelName string) (*models.GWModelConfig, error) {
	var model models.GWModelConfig
	err := s.DB.Where("provider = ? AND model_name = ? AND enabled = ?", provider, modelName, true).First(&model).Error
	if err != nil {
		return nil, fmt.Errorf("未找到模型配置: %s/%s", provider, modelName)
	}
	return &model, nil
}
