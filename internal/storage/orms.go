package storage

import (
	"context"
	mysqldriver "github.com/go-sql-driver/mysql"
	"goalfy_aigateway/internal/config"
	"goalfy_aigateway/internal/models"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"time"
)

type CommonDao struct {
	*gorm.DB
}

func Open() (CommonDao, error) {
	cfg := config.GetConfig().Database

	mysqlCfg := mysql.Config{
		DSN: cfg.DSN,
	}

	if cfg.DSN != "" {
		dsn, err := mysqldriver.ParseDSN(cfg.DSN)
		if err != nil {
			return CommonDao{}, err
		}
		dsn.Loc = time.Local
		dsn.ParseTime = true
		mysqlCfg.DSN = dsn.FormatDSN()
	}

	db, err := gorm.Open(mysql.New(mysqlCfg))
	if err != nil {
		return CommonDao{}, err
	}

	// 按依赖顺序迁移表
	err = db.AutoMigrate(
		&models.GWProvider{},
		&models.GWUserQuotaConfig{},
		&models.GWModelGroup{},
		&models.GWModelConfig{},    // 确保复合索引先创建
		&models.GWModelGroupItem{}, // 然后创建依赖表
	)
	if err != nil {
		return CommonDao{}, err
	}

	return CommonDao{db}, nil
}

func (d CommonDao) Create(ctx context.Context, dest interface{}) error {
	return d.DB.WithContext(ctx).Create(dest).Error
}

func (d CommonDao) List(ctx context.Context, dest interface{}, cond ...interface{}) error {
	return d.DB.WithContext(ctx).Find(dest, cond...).Error
}

func (d CommonDao) Save(ctx context.Context, dest interface{}) error {
	return d.DB.WithContext(ctx).Save(dest).Error
}

/*
// User's ID is `111`:
// Update attributes with `struct`, will only update non-zero fields

Db.Model(&user).Updates(User{Name: "hello", Age: 18, Active: false})
// UPDATE users SET name='hello', age=18, updated_at = '2013-11-17 21:34:10' WHERE id = 111;

Db.Model(&user).Updates(map[string]interface{}{"name": "hello", "age": 18, "active": false})
// UPDATE users SET name='hello', age=18, active=false, updated_at='2013-11-17 21:34:10' WHERE id=111;
*/
func (d CommonDao) Update(ctx context.Context, recordWithPk interface{}, conlumns interface{}) error {
	return d.DB.WithContext(ctx).Model(recordWithPk).Updates(conlumns).Error
}

func (d CommonDao) UpdateColumn(ctx context.Context, column string, value interface{}) error {
	return d.DB.WithContext(ctx).UpdateColumn(column, value).Error
}

func (d CommonDao) Take(ctx context.Context, dest interface{}, conds ...interface{}) error {
	return d.DB.WithContext(ctx).Take(dest, conds...).Error
}

func (d CommonDao) Delete(ctx context.Context, dest interface{}, conds ...interface{}) error {
	return d.DB.WithContext(ctx).Delete(dest, conds...).Error
}

func (d CommonDao) Exec(sql string, conds ...interface{}) error {
	return d.DB.Exec(sql, conds...).Error
}

func (d CommonDao) TakeWithTrashed(ctx context.Context, dest interface{}, conds ...interface{}) error {
	return d.DB.WithContext(ctx).Unscoped().Take(dest, conds...).Error
}
